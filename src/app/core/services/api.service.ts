import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Thực hiện một HTTP GET request
   * @param path Đường dẫn API (không bao gồm baseUrl)
   * @param params Tham số query string
   * @param options Tùy chọn HTTP khác
   * @returns Observable<ApiResponse<T>>
   */
  public get<T>(path: string, params: any = {}, options: any = {}): Observable<ApiResponse<T>> {
    const url = this.createUrl(path);
    const httpParams = this.createHttpParams(params);
    const httpOptions = this.createOptions(options, httpParams);

    return this.http
      .get(url, { ...httpOptions, responseType: 'json' })
      .pipe(map((response: any) => response as ApiResponse<T>));
  }

  /**
   * Thực hiện một HTTP POST request
   * @param path Đường dẫn API (không bao gồm baseUrl)
   * @param body Dữ liệu gửi đi
   * @param options Tùy chọn HTTP khác
   * @returns Observable<ApiResponse<T>>
   */
  public post<T>(path: string, body: any = {}, options: any = {}): Observable<ApiResponse<T>> {
    const url = this.createUrl(path);
    const httpOptions = this.createOptions(options);

    return this.http
      .post(url, body, { ...httpOptions, responseType: 'json' })
      .pipe(map((response: any) => response as ApiResponse<T>));
  }

  /**
   * Thực hiện một HTTP PUT request
   * @param path Đường dẫn API (không bao gồm baseUrl)
   * @param body Dữ liệu gửi đi
   * @param options Tùy chọn HTTP khác
   * @returns Observable<ApiResponse<T>>
   */
  public put<T>(path: string, body: any = {}, options: any = {}): Observable<ApiResponse<T>> {
    const url = this.createUrl(path);
    const httpOptions = this.createOptions(options);

    return this.http
      .put(url, body, { ...httpOptions, responseType: 'json' })
      .pipe(map((response: any) => response as ApiResponse<T>));
  }

  /**
   * Thực hiện một HTTP PATCH request
   * @param path Đường dẫn API (không bao gồm baseUrl)
   * @param body Dữ liệu gửi đi
   * @param options Tùy chọn HTTP khác
   * @returns Observable<ApiResponse<T>>
   */
  public patch<T>(path: string, body: any = {}, options: any = {}): Observable<ApiResponse<T>> {
    const url = this.createUrl(path);
    const httpOptions = this.createOptions(options);

    return this.http
      .patch(url, body, { ...httpOptions, responseType: 'json' })
      .pipe(map((response: any) => response as ApiResponse<T>));
  }

  /**
   * Thực hiện một HTTP DELETE request
   * @param path Đường dẫn API (không bao gồm baseUrl)
   * @param options Tùy chọn HTTP khác
   * @returns Observable<ApiResponse<T>>
   */
  public delete<T>(path: string, options: any = {}): Observable<ApiResponse<T>> {
    const url = this.createUrl(path);
    const httpOptions = this.createOptions(options);

    return this.http
      .delete(url, { ...httpOptions, responseType: 'json' })
      .pipe(map((response: any) => response as ApiResponse<T>));
  }

  /**
   * Tạo URL đầy đủ từ path
   * @param path Đường dẫn API
   * @returns URL đầy đủ
   */
  private createUrl(path: string): string {
    // Loại bỏ slash (/) ở đầu path nếu có
    if (path.startsWith('/')) {
      path = path.substring(1);
    }
    return `${this.apiUrl}/${path}`;
  }

  /**
   * Tạo HttpParams từ object
   * @param params Object chứa các tham số
   * @returns HttpParams
   */
  private createHttpParams(params: any): HttpParams {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return httpParams;
  }

  /**
   * Tạo options cho request
   * @param options Tùy chọn cung cấp
   * @param params HttpParams (tuỳ chọn)
   * @returns Options cho request
   */
  private createOptions(options: any, params?: HttpParams): any {
    const headers: any = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    // Get access token from localStorage
    const accessToken = localStorage.getItem('lbvd_token');
    if (accessToken) {
      try {
        headers['access-token'] = `${accessToken}`;
      } catch (error) {
        console.error('Error parsing user from localStorage', error);
      }
    }

    const defaultOptions = {
      headers: new HttpHeaders(headers),
      params: params || null,
    };

    return { ...defaultOptions, ...options };
  }
}
