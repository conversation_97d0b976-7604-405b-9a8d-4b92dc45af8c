import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, of, throwError, firstValueFrom } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { ApiService } from './api.service';
import { environment } from '../../../environments/environment';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UserModel,
  RefreshTokenRequest,
} from '../models/auth.model';
import {
  Address,
  Province,
  District,
  Ward,
  AddressFormData,
  AddressHelper,
} from '../models/address.model';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<UserModel | null>;
  public currentUser: Observable<UserModel | null>;
  private isAuthenticatedSubject: BehaviorSubject<boolean>;
  public isAuthenticated: Observable<boolean>;

  constructor(private apiService: ApiService, private router: Router) {
    // Khởi tạo user từ localStorage nếu đã đăng nhập
    const userJson = localStorage.getItem('user');
    let user: UserModel | null = null;

    try {
      if (userJson) {
        user = JSON.parse(userJson);
      }
    } catch (error) {
      console.error('Error parsing user from localStorage', error);
    }

    this.currentUserSubject = new BehaviorSubject<UserModel | null>(user);
    this.currentUser = this.currentUserSubject.asObservable();

    this.isAuthenticatedSubject = new BehaviorSubject<boolean>(!!user);
    this.isAuthenticated = this.isAuthenticatedSubject.asObservable();
  }

  /**
   * Lấy giá trị user hiện tại
   */
  public get currentUserValue(): UserModel | null {
    return this.currentUserSubject.value;
  }

  /**
   * Đăng nhập
   * @param credentials Thông tin đăng nhập (email, password)
   */
  public login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.apiService.post<any>('user/api/login.html', credentials).pipe(
      map(response => {
        console.log('Login response:', response);

        // API trả về cấu trúc {error, message, data}
        const authResponse: AuthResponse = {
          error: response.error || false,
          message: response.message || '',
          data: response.data,
        };

        // Đăng nhập thành công, xử lý lưu trữ thông tin
        if (!authResponse.error && authResponse.data?.token && authResponse.data?.user) {
          // Lưu token vào localStorage
          this.saveToken(authResponse.data.token, authResponse.data.token);

          // Lưu thông tin user vào localStorage
          this.saveUser(authResponse.data.user);

          // Cập nhật BehaviorSubject
          this.currentUserSubject.next(authResponse.data.user);
          this.isAuthenticatedSubject.next(true);
        }

        return authResponse;
      }),
      catchError(error => {
        console.error('Login error', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Đăng ký
   * @param userData Thông tin đăng ký
   */
  public register(userData: RegisterRequest): Observable<AuthResponse> {
    return this.apiService.post<any>('user/api/register-by-phone.html', userData).pipe(
      map(response => {
        console.log('Register response:', response);

        // API trả về cấu trúc {error, message, data}
        const authResponse: AuthResponse = {
          error: response.error || false,
          message: response.message || '',
          data: response.data,
        };

        // Đăng ký thành công, xử lý lưu trữ thông tin
        if (!authResponse.error && authResponse.data?.token && authResponse.data?.user) {
          // Lưu token vào localStorage
          this.saveToken(authResponse.data.token, authResponse.data.token);

          // Lưu thông tin user vào localStorage
          this.saveUser(authResponse.data.user);

          // Cập nhật BehaviorSubject
          this.currentUserSubject.next(authResponse.data.user);
          this.isAuthenticatedSubject.next(true);
        }

        return authResponse;
      }),
      catchError(error => {
        console.error('Register error', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Đăng xuất
   */
  public logout(): void {
    // Xóa token và user khỏi localStorage
    localStorage.removeItem(environment.tokenKey);
    localStorage.removeItem(environment.refreshTokenKey);
    localStorage.removeItem('user');

    // Reset BehaviorSubject
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);

    // Điều hướng về trang đăng nhập
    this.router.navigate(['/dang-nhap']);
  }

  /**
   * Refresh token
   */
  public refreshToken(): Observable<AuthResponse> {
    const refreshToken = localStorage.getItem(environment.refreshTokenKey);

    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    const refreshTokenRequest: RefreshTokenRequest = {
      refreshToken: refreshToken,
    };

    return this.apiService.post<any>('auth/refresh-token', refreshTokenRequest).pipe(
      map(response => {
        console.log('Refresh token response:', response);

        // API trả về cấu trúc {error, message, data}
        const authResponse: AuthResponse = {
          error: response.error || false,
          message: response.message || '',
          data: response.data,
        };

        // Refresh token thành công, có data.token
        if (!authResponse.error && authResponse.data?.token) {
          // Lưu token mới vào localStorage
          this.saveToken(authResponse.data.token, authResponse.data.token);
        }

        return authResponse;
      }),
      catchError(error => {
        console.error('Refresh token error', error);
        // Nếu refresh token thất bại, đăng xuất người dùng
        this.logout();
        return throwError(() => error);
      }),
    );
  }

  /**
   * Kiểm tra token và lấy thông tin người dùng
   */
  public checkToken(): Observable<AuthResponse> {
    const token = localStorage.getItem(environment.tokenKey);

    if (!token) {
      return throwError(() => new Error('No token available'));
    }

    return this.apiService.post<any>('user/api/check-token.html', { token }).pipe(
      map(response => {
        console.log('Check token response:', response);

        // API trả về cấu trúc {error, message, data}
        const authResponse: AuthResponse = {
          error: response.error || false,
          message: response.message || '',
          data: response.data,
        };

        // Token hợp lệ, xử lý user data
        if (!authResponse.error && authResponse.data?.user) {
          // Lưu thông tin user vào localStorage
          this.saveUser(authResponse.data.user);

          // Cập nhật BehaviorSubject
          this.currentUserSubject.next(authResponse.data.user);
          this.isAuthenticatedSubject.next(true);
        }

        return authResponse;
      }),
      catchError(error => {
        console.error('Check token error', error);
        // Nếu token không hợp lệ, đăng xuất người dùng
        this.logout();
        return throwError(() => error);
      }),
    );
  }

  /**
   * Lấy thông tin user từ server
   */
  public getUserProfile(): Observable<UserModel> {
    return this.apiService.get<any>('users/profile').pipe(
      map(response => {
        console.log('Get user profile response:', response);

        // API trả về cấu trúc {error, message, data}
        if (!response.error && response.data?.user) {
          const userData = response.data.user;

          // Cập nhật thông tin user trong localStorage
          this.saveUser(userData);

          // Cập nhật BehaviorSubject
          this.currentUserSubject.next(userData);

          return userData;
        }
        return {} as UserModel;
      }),
      catchError(error => {
        console.error('Get user profile error', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Lưu token vào localStorage
   */
  private saveToken(token: string | undefined, refreshToken: string | undefined): void {
    if (token) {
      localStorage.setItem(environment.tokenKey, token);
    }

    if (refreshToken) {
      localStorage.setItem(environment.refreshTokenKey, refreshToken);
    }
  }

  /**
   * Lưu thông tin user vào localStorage
   */
  private saveUser(user: UserModel | undefined): void {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    }
  }

  // ==================== ADDRESS MANAGEMENT METHODS ====================

  /**
   * Lấy danh sách địa chỉ của user
   */
  public getAddressList(): Observable<Address[]> {
    return this.apiService.get<any>('user/api/get-profile.html').pipe(
      map(response => {
        if (!response.error && response.data?.user?.addressList) {
          return response.data.user.addressList;
        }
        return [];
      }),
      catchError(error => {
        console.error('Get address list error', error);
        return of([]);
      }),
    );
  }

  /**
   * Thêm địa chỉ mới
   */
  public async addAddress(
    addressData: AddressFormData,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate address data
      const validation = AddressHelper.validateAddress(addressData);
      if (!validation.valid) {
        return {
          success: false,
          message: validation.errors.join(', '),
        };
      }

      // Get current user profile
      const currentUser = this.currentUserValue;
      if (!currentUser) {
        return {
          success: false,
          message: 'Vui lòng đăng nhập để thêm địa chỉ',
        };
      }

      // Get current address list
      const addressList = await firstValueFrom(this.getAddressList());

      // Create new address
      const newAddress = AddressHelper.createAddressFromForm(addressData);

      // If this is the first address, make it default
      if (addressList.length === 0) {
        newAddress.default = true;
      }

      // Add to address list
      const updatedAddressList = [...addressList, newAddress];

      // Update profile with new address list
      const updateData = {
        ...currentUser,
        addressList: updatedAddressList,
      };

      const response = await firstValueFrom(
        this.apiService.post<any>('user/api/update-profile.html', updateData),
      );

      if (!response.error) {
        // Update current user data
        const updatedUser = { ...currentUser, addressList: updatedAddressList };
        this.saveUser(updatedUser);
        this.currentUserSubject.next(updatedUser);

        return {
          success: true,
          message: 'Thêm địa chỉ thành công',
        };
      } else {
        return {
          success: false,
          message: response.message || 'Có lỗi xảy ra khi thêm địa chỉ',
        };
      }
    } catch (error) {
      console.error('Add address error', error);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi thêm địa chỉ',
      };
    }
  }

  /**
   * Cập nhật địa chỉ
   */
  public async updateAddress(
    addressId: string,
    addressData: AddressFormData,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate address data
      const validation = AddressHelper.validateAddress(addressData);
      if (!validation.valid) {
        return {
          success: false,
          message: validation.errors.join(', '),
        };
      }

      const currentUser = this.currentUserValue;
      if (!currentUser) {
        return {
          success: false,
          message: 'Vui lòng đăng nhập để cập nhật địa chỉ',
        };
      }

      // Get current address list
      const addressList = await firstValueFrom(this.getAddressList());

      // Find address to update
      const addressIndex = addressList.findIndex(addr => addr._id === addressId);
      if (addressIndex === -1) {
        return {
          success: false,
          message: 'Không tìm thấy địa chỉ cần cập nhật',
        };
      }

      // Update address
      const updatedAddress = AddressHelper.createAddressFromForm(addressData);
      updatedAddress._id = addressId;

      // If setting as default, remove default from others
      if (addressData.default) {
        addressList.forEach(addr => (addr.default = false));
        updatedAddress.default = true;
      } else {
        updatedAddress.default = addressList[addressIndex].default;
      }

      addressList[addressIndex] = updatedAddress;

      // Update profile
      const updateData = {
        ...currentUser,
        addressList: addressList,
      };

      const response = await firstValueFrom(
        this.apiService.post<any>('user/api/update-profile.html', updateData),
      );

      if (!response.error) {
        // Update current user data
        const updatedUser = { ...currentUser, addressList: addressList };
        this.saveUser(updatedUser);
        this.currentUserSubject.next(updatedUser);

        return {
          success: true,
          message: 'Cập nhật địa chỉ thành công',
        };
      } else {
        return {
          success: false,
          message: response.message || 'Có lỗi xảy ra khi cập nhật địa chỉ',
        };
      }
    } catch (error) {
      console.error('Update address error', error);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi cập nhật địa chỉ',
      };
    }
  }

  /**
   * Xóa địa chỉ
   */
  public async deleteAddress(addressId: string): Promise<{ success: boolean; message: string }> {
    try {
      const currentUser = this.currentUserValue;
      if (!currentUser) {
        return {
          success: false,
          message: 'Vui lòng đăng nhập để xóa địa chỉ',
        };
      }

      // Get current address list
      const addressList = await firstValueFrom(this.getAddressList());

      // Find address to delete
      const addressIndex = addressList.findIndex(addr => addr._id === addressId);
      if (addressIndex === -1) {
        return {
          success: false,
          message: 'Không tìm thấy địa chỉ cần xóa',
        };
      }

      const addressToDelete = addressList[addressIndex];

      // Remove address from list
      addressList.splice(addressIndex, 1);

      // If deleted address was default and there are other addresses, set first one as default
      if (addressToDelete.default && addressList.length > 0) {
        addressList[0].default = true;
      }

      // Update profile
      const updateData = {
        ...currentUser,
        addressList: addressList,
      };

      const response = await firstValueFrom(
        this.apiService.post<any>('user/api/update-profile.html', updateData),
      );

      if (!response.error) {
        // Update current user data
        const updatedUser = { ...currentUser, addressList: addressList };
        this.saveUser(updatedUser);
        this.currentUserSubject.next(updatedUser);

        return {
          success: true,
          message: 'Xóa địa chỉ thành công',
        };
      } else {
        return {
          success: false,
          message: response.message || 'Có lỗi xảy ra khi xóa địa chỉ',
        };
      }
    } catch (error) {
      console.error('Delete address error', error);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi xóa địa chỉ',
      };
    }
  }

  /**
   * Đặt địa chỉ làm mặc định
   */
  public async setDefaultAddress(
    addressId: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const currentUser = this.currentUserValue;
      if (!currentUser) {
        return {
          success: false,
          message: 'Vui lòng đăng nhập để đặt địa chỉ mặc định',
        };
      }

      // Get current address list
      const addressList = await firstValueFrom(this.getAddressList());

      // Find address to set as default
      const addressIndex = addressList.findIndex(addr => addr._id === addressId);
      if (addressIndex === -1) {
        return {
          success: false,
          message: 'Không tìm thấy địa chỉ',
        };
      }

      // Remove default from all addresses
      addressList.forEach(addr => (addr.default = false));

      // Set selected address as default
      addressList[addressIndex].default = true;

      // Update profile
      const updateData = {
        ...currentUser,
        addressList: addressList,
      };

      const response = await firstValueFrom(
        this.apiService.post<any>('user/api/update-profile.html', updateData),
      );

      if (!response.error) {
        // Update current user data
        const updatedUser = { ...currentUser, addressList: addressList };
        this.saveUser(updatedUser);
        this.currentUserSubject.next(updatedUser);

        return {
          success: true,
          message: 'Đặt địa chỉ mặc định thành công',
        };
      } else {
        return {
          success: false,
          message: response.message || 'Có lỗi xảy ra khi đặt địa chỉ mặc định',
        };
      }
    } catch (error) {
      console.error('Set default address error', error);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi đặt địa chỉ mặc định',
      };
    }
  }

  /**
   * Lấy danh sách tỉnh/thành phố
   */
  public getProvinces(): Observable<Province[]> {
    return this.apiService.get<any>('user/api/get-provinces').pipe(
      map(response => {
        if (!response.error && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(error => {
        console.error('Get provinces error', error);
        return of([]);
      }),
    );
  }

  /**
   * Lấy danh sách quận/huyện theo tỉnh
   */
  public getDistricts(provinceCode: string): Observable<District[]> {
    return this.apiService
      .get<any>(`user/api/get-district-by-province?province=${provinceCode}`)
      .pipe(
        map(response => {
          if (!response.error && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError(error => {
          console.error('Get districts error', error);
          return of([]);
        }),
      );
  }

  /**
   * Lấy danh sách phường/xã theo quận/huyện
   */
  public getWards(districtCode: string): Observable<Ward[]> {
    return this.apiService.get<any>(`user/api/get-ward-by-district?district=${districtCode}`).pipe(
      map(response => {
        if (!response.error && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(error => {
        console.error('Get wards error', error);
        return of([]);
      }),
    );
  }
}
