import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { 
  CartItem, 
  ShopCart, 
  CartSummary, 
  CART_STORAGE_KEY,
  ProductInfo,
  ClassifyOption
} from '../models/cart.model';
import { ApiService } from './api.service';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private cartSubject = new BehaviorSubject<CartItem[]>([]);
  private cartSummarySubject = new BehaviorSubject<CartSummary>({
    totalPrice: 0,
    totalCount: 0,
    selectedItems: [],
    selectedShops: []
  });

  public cart$ = this.cartSubject.asObservable();
  public cartSummary$ = this.cartSummarySubject.asObservable();

  constructor(
    private apiService: ApiService,
    private authService: AuthService
  ) {
    this.loadCartFromStorage();
  }

  /**
   * Load cart from localStorage
   */
  private loadCartFromStorage(): void {
    try {
      const cartData = localStorage.getItem(CART_STORAGE_KEY);
      if (cartData) {
        const items = JSON.parse(cartData);
        this.cartSubject.next(items);
        this.updateCartSummary();
      }
    } catch (error) {
      console.error('Error loading cart from storage:', error);
      this.clearCart();
    }
  }

  /**
   * Save cart to localStorage
   */
  private saveCartToStorage(items: CartItem[]): void {
    try {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  }

  /**
   * Get current cart items
   */
  getCartItems(): CartItem[] {
    return this.cartSubject.value;
  }

  /**
   * Add product to cart
   */
  async addToCart(
    productId: string, 
    count: number, 
    shopId: string, 
    classifyActive?: ClassifyOption, 
    noteProduct: string = '',
    productInfo?: ProductInfo
  ): Promise<void> {
    const currentCart = this.getCartItems();
    
    // Generate unique product ID with classify info
    const uniqueProductId = classifyActive 
      ? `${productId}@${classifyActive._id.toLowerCase().trim()}`
      : productId;

    // Check if product already exists
    const existingIndex = currentCart.findIndex(item => item.productId === uniqueProductId);

    if (existingIndex >= 0) {
      // Update existing item
      currentCart[existingIndex].count += count;
      currentCart[existingIndex].noteProduct = noteProduct;
      
      // Move to top
      const item = currentCart.splice(existingIndex, 1)[0];
      currentCart.unshift(item);
    } else {
      // Add new item
      const newItem: CartItem = {
        productId: uniqueProductId,
        count,
        shopId,
        classifyActive,
        noteProduct,
        info: productInfo,
        selected: true
      };
      currentCart.unshift(newItem);
    }

    // Clean up null/undefined items
    const cleanCart = currentCart.filter(item => item && item.productId);
    
    this.cartSubject.next(cleanCart);
    this.saveCartToStorage(cleanCart);
    this.updateCartSummary();
  }

  /**
   * Update item quantity
   */
  updateQuantity(productId: string, count: number): void {
    const currentCart = this.getCartItems();
    const itemIndex = currentCart.findIndex(item => item.productId === productId);
    
    if (itemIndex >= 0) {
      if (count <= 0) {
        this.removeFromCart(productId);
      } else {
        currentCart[itemIndex].count = count;
        this.cartSubject.next(currentCart);
        this.saveCartToStorage(currentCart);
        this.updateCartSummary();
      }
    }
  }

  /**
   * Update item note
   */
  updateNote(productId: string, note: string): void {
    const currentCart = this.getCartItems();
    const itemIndex = currentCart.findIndex(item => item.productId === productId);
    
    if (itemIndex >= 0) {
      currentCart[itemIndex].noteProduct = note;
      this.cartSubject.next(currentCart);
      this.saveCartToStorage(currentCart);
    }
  }

  /**
   * Remove item from cart
   */
  removeFromCart(productId: string): void {
    const currentCart = this.getCartItems();
    const filteredCart = currentCart.filter(item => item.productId !== productId);
    
    this.cartSubject.next(filteredCart);
    this.saveCartToStorage(filteredCart);
    this.updateCartSummary();
  }

  /**
   * Toggle item selection
   */
  toggleItemSelection(productId: string): void {
    const currentCart = this.getCartItems();
    const item = currentCart.find(item => item.productId === productId);
    
    if (item) {
      item.selected = !item.selected;
      this.cartSubject.next(currentCart);
      this.saveCartToStorage(currentCart);
      this.updateCartSummary();
    }
  }

  /**
   * Clear cart
   */
  clearCart(): void {
    this.cartSubject.next([]);
    localStorage.removeItem(CART_STORAGE_KEY);
    this.updateCartSummary();
  }

  /**
   * Remove purchased items from cart
   */
  removePurchasedItems(purchasedItems: CartItem[]): void {
    const currentCart = this.getCartItems();
    const purchasedIds = purchasedItems.map(item => item.productId);
    const remainingCart = currentCart.filter(item => !purchasedIds.includes(item.productId));
    
    this.cartSubject.next(remainingCart);
    this.saveCartToStorage(remainingCart);
    this.updateCartSummary();
  }

  /**
   * Get cart information from API
   */
  async fetchCartInformation(): Promise<ShopCart[]> {
    const cartItems = this.getCartItems();
    if (cartItems.length === 0) {
      return [];
    }

    // Group items by shop
    const shopProducts = this.groupItemsByShop(cartItems);
    
    try {
      const response = await this.apiService.post('user/api/tai-thong-tin-gio-hang.html', 
        { shopProducts }, 
        { output: 'json' }
      ).toPromise();

      if (response && !response.error && response.data?.shopProducts) {
        const shops: ShopCart[] = response.data.shopProducts.map((shop: any) => ({
          ...shop,
          selected: true,
          haveItem: true,
          products: shop[shop.info._id].products.map((product: any) => ({
            ...product,
            selected: true
          }))
        }));

        // Handle removed products
        if (response.data.removeProducts && response.data.removeProducts.length > 0) {
          response.data.removeProducts.forEach((productId: string) => {
            this.removeFromCart(productId);
          });
        }

        return shops;
      }
    } catch (error) {
      console.error('Error fetching cart information:', error);
    }

    return [];
  }

  /**
   * Group cart items by shop
   */
  private groupItemsByShop(items: CartItem[]): any[] {
    const groupByShop: { [shopId: string]: { products: CartItem[] } } = {};
    
    items.forEach(item => {
      if (groupByShop[item.shopId]) {
        groupByShop[item.shopId].products.push(item);
      } else {
        groupByShop[item.shopId] = {
          products: [item]
        };
      }
    });

    const result = [];
    for (const shopId in groupByShop) {
      result.push({ [shopId]: groupByShop[shopId] });
    }
    
    return result;
  }

  /**
   * Update cart summary
   */
  private updateCartSummary(): void {
    const items = this.getCartItems();
    const selectedItems = items.filter(item => item.selected);
    
    let totalPrice = 0;
    let totalCount = 0;

    selectedItems.forEach(item => {
      const price = item.classifyActive?.price 
        ? parseInt(item.classifyActive.price.replace(/,/g, ''))
        : item.info?.price || 0;
      
      totalPrice += price * item.count;
      totalCount += item.count;
    });

    const summary: CartSummary = {
      totalPrice,
      totalCount,
      selectedItems,
      selectedShops: [] // Will be populated when needed
    };

    this.cartSummarySubject.next(summary);
  }

  /**
   * Get cart count
   */
  getCartCount(): number {
    return this.getCartItems().reduce((total, item) => total + item.count, 0);
  }

  /**
   * Get selected items count
   */
  getSelectedCount(): number {
    return this.getCartItems()
      .filter(item => item.selected)
      .reduce((total, item) => total + item.count, 0);
  }
}
