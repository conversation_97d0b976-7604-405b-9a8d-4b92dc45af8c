import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';
import {
  OrderHistory,
  OrderHistoryResponse,
  OrderDetailResponse,
  ORDER_STATUS,
  PAYMENT_METHOD,
} from '../models/order.model';

@Injectable({
  providedIn: 'root',
})
export class OrderHistoryService {
  constructor(private apiService: ApiService) {}

  /**
   * Get order history with pagination
   */
  getOrderHistory(page: number = 1, limit: number = 10): Observable<OrderHistoryResponse> {
    const params = {
      page: page.toString(),
      limit: limit.toString(),
      output: 'json',
    };

    return this.apiService.get<any>('user/api/lay-lich-su-mua-hang/', params).pipe(
      map(response => {
        console.log('📋 Order history response:', response);

        if (!response.error && response.data) {
          // Process orders data from lichsu array
          const orders = this.processOrdersData(response.data.lichsu || []);
          const totalPages = response.data.totalPage || 1;

          return {
            error: false,
            message: response.message || 'Thành công',
            data: {
              orders: orders,
              total: orders.length * totalPages, // Estimate total from current data
              page: page,
              limit: limit,
              totalPages: totalPages,
            },
          };
        }

        return {
          error: true,
          message: response.message || 'Không thể tải lịch sử đơn hàng',
          data: {
            orders: [],
            total: 0,
            page: page,
            limit: limit,
            totalPages: 0,
          },
        };
      }),
      catchError(error => {
        console.error('❌ Error fetching order history:', error);
        return of({
          error: true,
          message: 'Có lỗi xảy ra khi tải lịch sử đơn hàng',
          data: {
            orders: [],
            total: 0,
            page: page,
            limit: limit,
            totalPages: 0,
          },
        });
      }),
    );
  }

  /**
   * Get order detail by ID
   */
  getOrderDetail(orderId: string): Observable<OrderDetailResponse> {
    return this.apiService
      .get<any>(`user/api/chi-tiet-don-hang/${orderId}`, { output: 'json' })
      .pipe(
        map(response => {
          console.log('📄 Order detail response:', response);

          if (!response.error && response.data) {
            const order = this.processOrderData(response.data);

            return {
              error: false,
              message: response.message || 'Thành công',
              data: order,
            };
          }

          return {
            error: true,
            message: response.message || 'Không thể tải chi tiết đơn hàng',
            data: {} as OrderHistory,
          };
        }),
        catchError(error => {
          console.error('❌ Error fetching order detail:', error);
          return of({
            error: true,
            message: 'Có lỗi xảy ra khi tải chi tiết đơn hàng',
            data: {} as OrderHistory,
          });
        }),
      );
  }

  /**
   * Process orders data from API response
   */
  private processOrdersData(orders: any[]): OrderHistory[] {
    if (!Array.isArray(orders)) {
      return [];
    }

    return orders.map(order => this.processOrderData(order));
  }

  /**
   * Process single order data
   */
  private processOrderData(order: any): OrderHistory {
    return {
      _id: order._id || '',
      orderId: order.orderId || order.paymentId || '',
      userId: order.userId || '',
      storeId: order.storeId || '',
      store: {
        _id: order.storeId || '',
        name: order.storeName || 'Cửa hàng',
        address: this.buildStoreAddress(order),
        phone: order.storePhone || '',
      },
      products: this.processProductsData(order.products || []),
      name: order.userBuyFullName || order.name || '',
      phone: order.phone || '',
      province: order.province || '',
      district: order.district || '',
      ward: order.ward || '',
      street: order.street || '',
      location: order.location || order.address || this.buildFullAddress(order),
      note: order.note || '',
      paymentMethod: order.paymentMethod || order.isPayOnline || 0,
      bankCode: order.bankCode || '',
      totalAmount: Number(order.totalPriceShop || order.totalAmount || 0),
      transportFee: Number(order.transportFee || 0),
      couponCode: order.coupon || order.couponCode || '',
      couponDiscount: Number(order.couponDiscount || 0),
      status: order.status || 0,
      statusText:
        order.statusText ||
        ORDER_STATUS[order.status as keyof typeof ORDER_STATUS] ||
        'Không xác định',
      createdAt: order.createAt ? new Date(order.createAt).toISOString() : new Date().toISOString(),
      updatedAt: order.modifyAt ? new Date(order.modifyAt).toISOString() : new Date().toISOString(),
      bookingType: order.bookingType || 0,
      shippingService: order.shippingService || '',
      trackingCode: order.trackingCode || '',
    };
  }

  /**
   * Process products data
   */
  private processProductsData(products: any[]): any[] {
    if (!Array.isArray(products)) {
      return [];
    }

    return products.map(product => {
      // Handle classifyActive array from API
      let classifyActive = null;
      if (
        product.classifyActive &&
        Array.isArray(product.classifyActive) &&
        product.classifyActive.length > 0
      ) {
        classifyActive = product.classifyActive[0]; // Take first item from array
      } else if (product.classifyActive && !Array.isArray(product.classifyActive)) {
        classifyActive = product.classifyActive;
      }

      return {
        _id: product._id || '',
        productId: product.productId || '',
        name: product.name || product.info?.name || classifyActive?.name || 'Sản phẩm',
        price: Number(product.price || product.info?.price || 0),
        count: Number(product.count || 1),
        weight: Number(product.weight || product.info?.weight || classifyActive?.mass || 0),
        thumbnail: product.thumbnail || product.info?.thumbail || '',
        classifyActive: classifyActive,
        info: product.info || null,
        noteProduct: product.noteProduct || '',
      };
    });
  }

  /**
   * Build full address from components
   */
  private buildFullAddress(order: any): string {
    const parts = [order.street, order.ward, order.district, order.province].filter(
      part => part && part.trim(),
    );

    return parts.join(', ');
  }

  /**
   * Build store address from components
   */
  private buildStoreAddress(order: any): string {
    if (order.storeAddress) {
      return order.storeAddress;
    }

    const parts = [
      order.storeStreet,
      order.storeWard,
      order.storeDistrict,
      order.storeProvince,
    ].filter(part => part && part.trim());

    return parts.join(', ');
  }

  /**
   * Get payment method text
   */
  getPaymentMethodText(paymentMethod: number): string {
    return PAYMENT_METHOD[paymentMethod as keyof typeof PAYMENT_METHOD] || 'Không xác định';
  }

  /**
   * Get order status text
   */
  getOrderStatusText(status: number): string {
    return ORDER_STATUS[status as keyof typeof ORDER_STATUS] || 'Không xác định';
  }

  /**
   * Format price to VND
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }
}
