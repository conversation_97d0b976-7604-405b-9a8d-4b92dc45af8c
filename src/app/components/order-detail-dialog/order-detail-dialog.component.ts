import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';

import { TablerIconsModule } from 'angular-tabler-icons';
import { OrderHistory, ORDER_STATUS_COLORS } from '../../core/models/order.model';
import { OrderHistoryService } from '../../core/services/order-history.service';

@Component({
  selector: 'app-order-detail-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    TablerIconsModule,
  ],
  template: `
    <div class="order-detail-dialog">
      <!-- Header -->
      <!-- <div class="dialog-header">
        <h2 mat-dialog-title>Chi tiết đơn hàng</h2>
        <button mat-icon-button (click)="onClose()" class="close-button">
          <i-tabler name="x"></i-tabler>
        </button>
      </div> -->

      <!-- Content -->
      <div mat-dialog-content class="dialog-content">
        <div *ngIf="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>Đang tải chi tiết đơn hàng...</p>
        </div>

        <div *ngIf="!loading && order" class="order-details">
          <!-- Order Info -->
          <div class="info-section">
            <div class="section-header">
              <i-tabler name="receipt" class="section-icon"></i-tabler>
              <h3>Thông tin đơn hàng</h3>
            </div>

            <div class="info-grid">
              <div class="info-item">
                <span class="label">Mã đơn hàng:</span>
                <span class="value order-id">{{ order.orderId }}</span>
              </div>
              <!-- <div class="info-item">
                <span class="label">Trạng thái:</span>
                <span class="status-badge" [style.background-color]="getStatusColor(order.status)">
                  {{ order.statusText }}
                </span>
              </div> -->
              <div class="info-item">
                <span class="label">Ngày đặt:</span>
                <span class="value">{{ order.createdAt | date : 'dd/MM/yyyy HH:mm' }}</span>
              </div>
              <div class="info-item">
                <span class="label">Phương thức thanh toán:</span>
                <span class="value">{{ getPaymentMethodText(order.paymentMethod) }}</span>
              </div>
              <div class="info-item" *ngIf="order.bankCode">
                <span class="label">Ngân hàng:</span>
                <span class="value">{{ order.bankCode }}</span>
              </div>
              <div class="info-item" *ngIf="order.trackingCode">
                <span class="label">Mã vận đơn:</span>
                <span class="value tracking-code">{{ order.trackingCode }}</span>
              </div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <!-- Customer Info -->
          <div class="info-section">
            <div class="section-header">
              <i-tabler name="user" class="section-icon"></i-tabler>
              <h3>Thông tin khách hàng</h3>
            </div>

            <div class="customer-info">
              <div class="info-item">
                <span class="label">Họ và tên:</span>
                <span class="value">{{ order.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">Số điện thoại:</span>
                <span class="value">{{ order.phone }}</span>
              </div>
              <div class="info-item">
                <span class="label">Địa chỉ giao hàng:</span>
                <span class="value address">{{ order.location }}</span>
              </div>
              <div class="info-item" *ngIf="order.note">
                <span class="label">Ghi chú:</span>
                <span class="value">{{ order.note }}</span>
              </div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <!-- Store Info -->
          <!-- <div class="info-section" *ngIf="order.store">
            <div class="section-header">
              <i-tabler name="building-store" class="section-icon"></i-tabler>
              <h3>Thông tin cửa hàng</h3>
            </div>

            <div class="store-info">
              <div class="info-item">
                <span class="label">Tên cửa hàng:</span>
                <span class="value">{{ order.store.name }}</span>
              </div>
              <div class="info-item" *ngIf="order.store.address">
                <span class="label">Địa chỉ:</span>
                <span class="value">{{ order.store.address }}</span>
              </div>
              <div class="info-item" *ngIf="order.store.phone">
                <span class="label">Số điện thoại:</span>
                <span class="value">{{ order.store.phone }}</span>
              </div>
            </div>
          </div>

          <mat-divider></mat-divider> -->

          <!-- Products -->
          <div class="info-section">
            <div class="section-header">
              <i-tabler name="package" class="section-icon"></i-tabler>
              <h3>Sản phẩm đã đặt</h3>
            </div>

            <div class="products-list">
              <div class="product-item" *ngFor="let product of order.products">
                <div class="product-image">
                  <img
                    [src]="getProductImage(product)"
                    [alt]="product.name"
                    (error)="onImageError($event)"
                  />
                </div>
                <div class="product-info">
                  <h4 class="product-name">{{ product.name }}</h4>
                  <div class="product-details">
                    <div class="product-classify" *ngIf="product.classifyActive">
                      <span class="classify-label">Phân loại:</span>
                      <span class="classify-value">{{ product.classifyActive.name }}</span>
                    </div>
                    <div class="product-quantity">
                      <span class="quantity-label">Số lượng:</span>
                      <span class="quantity-value">{{ product.count }}</span>
                    </div>
                    <div class="product-note" *ngIf="product.noteProduct">
                      <span class="note-label">Ghi chú:</span>
                      <span class="note-value">{{ product.noteProduct }}</span>
                    </div>
                  </div>
                </div>
                <div class="product-price">
                  <span class="price-value">{{ formatPrice(product.price * product.count) }}</span>
                  <span class="price-unit" *ngIf="product.count > 1">
                    ({{ formatPrice(product.price) }}/sp)
                  </span>
                </div>
              </div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <!-- Order Summary -->
          <div class="info-section">
            <div class="section-header">
              <i-tabler name="calculator" class="section-icon"></i-tabler>
              <h3>Tổng kết đơn hàng</h3>
            </div>

            <div class="order-summary">
              <div class="summary-item">
                <span class="label">Tạm tính:</span>
                <span class="value">{{ formatPrice(getSubtotal()) }}</span>
              </div>
              <div class="summary-item" *ngIf="order.transportFee > 0">
                <span class="label">Phí vận chuyển:</span>
                <span class="value">{{ formatPrice(order.transportFee) }}</span>
              </div>
              <div class="summary-item" *ngIf="order.couponDiscount && order.couponDiscount > 0">
                <span class="label">Giảm giá:</span>
                <span class="value discount">-{{ formatPrice(order.couponDiscount) }}</span>
              </div>
              <div class="summary-item total">
                <span class="label">Tổng cộng:</span>
                <span class="value">{{ formatPrice(order.totalAmount) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="!loading && !order" class="error-state">
          <i-tabler name="alert-circle" class="error-icon"></i-tabler>
          <p>Không thể tải thông tin đơn hàng</p>
        </div>
      </div>

      <!-- Actions -->
      <div mat-dialog-actions class="dialog-actions">
        <button mat-button (click)="onClose()">Đóng</button>
        <!-- <button mat-raised-button color="primary" *ngIf="order" (click)="onReorder()">
          <i-tabler name="refresh" class="button-icon"></i-tabler>
          Đặt lại
        </button> -->
      </div>
    </div>
  `,
  styles: [
    `
      .order-detail-dialog {
        width: 100%;
        max-width: 800px;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
      }

      .dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 2px solid #e9ecef;

        h2 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
          color: #333;
        }

        .close-button {
          color: #666;
        }
      }

      .dialog-content {
        flex: 1;
        overflow-y: auto;
        padding: 0;
      }

      .loading-state,
      .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        text-align: center;
        color: #666;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #0d6efd;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        .error-icon {
          width: 48px;
          height: 48px;
          color: #dc3545;
          margin-bottom: 1rem;
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .order-details {
        .info-section {
          padding: 1.5rem;

          .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.25rem;

            .section-icon {
              width: 20px;
              height: 20px;
              color: #0d6efd;
            }

            h3 {
              margin: 0;
              font-size: 1.1rem;
              font-weight: 600;
              color: #333;
            }
          }

          .info-grid,
          .customer-info,
          .store-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
          }

          .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;

            .label {
              font-size: 0.875rem;
              font-weight: 500;
              color: #666;
            }

            .value {
              font-size: 0.95rem;
              color: #333;

              &.order-id {
                font-family: monospace;
                font-weight: 600;
                color: #0d6efd;
              }

              &.tracking-code {
                font-family: monospace;
                font-weight: 600;
                color: #28a745;
              }

              &.address {
                line-height: 1.4;
              }

              &.discount {
                color: #28a745;
                font-weight: 600;
              }
            }
          }

          .status-badge {
            display: inline-block;
            font-size: 14px;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 16px;
            text-align: center;
            min-width: 80px;
          }
        }

        .products-list {
          .product-item {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 1rem;

            &:last-child {
              margin-bottom: 0;
            }

            .product-image {
              width: 80px;
              height: 80px;
              flex-shrink: 0;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 6px;
                background-color: #f8f9fa;
              }
            }

            .product-info {
              flex: 1;

              .product-name {
                margin: 0 0 0.5rem 0;
                font-size: 1rem;
                font-weight: 600;
                color: #333;
                line-height: 1.3;
              }

              .product-details {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;

                > div {
                  display: flex;
                  gap: 0.5rem;
                  font-size: 0.875rem;

                  .classify-label,
                  .quantity-label,
                  .note-label {
                    color: #666;
                    font-weight: 500;
                  }

                  .classify-value,
                  .quantity-value,
                  .note-value {
                    color: #333;
                  }
                }
              }
            }

            .product-price {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              justify-content: center;
              text-align: right;

              .price-value {
                font-size: 1rem;
                font-weight: 600;
                color: #0d6efd;
              }

              .price-unit {
                font-size: 0.75rem;
                color: #666;
                margin-top: 0.25rem;
              }
            }
          }
        }

        .order-summary {
          .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;

            &:last-child {
              border-bottom: none;
            }

            &.total {
              border-top: 2px solid #e9ecef;
              margin-top: 0.5rem;
              padding-top: 1rem;
              font-size: 1.1rem;
              font-weight: 600;

              .value {
                color: #0d6efd;
                font-size: 1.25rem;
              }
            }

            .label {
              color: #666;
              font-weight: 500;
            }

            .value {
              color: #333;
              font-weight: 600;
            }
          }
        }
      }

      .dialog-actions {
        padding: 1.5rem;
        border-top: 2px solid #e9ecef;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;

        .button-icon {
          width: 16px;
          height: 16px;
          margin-right: 0.5rem;
        }
      }

      @media (max-width: 768px) {
        .order-detail-dialog {
          max-width: 100vw;
          max-height: 100vh;
          margin: 0;
        }

        .dialog-content {
          .order-details .info-section {
            padding: 1rem;

            .info-grid,
            .customer-info,
            .store-info {
              grid-template-columns: 1fr;
            }

            .products-list .product-item {
              flex-direction: column;
              text-align: center;

              .product-image {
                align-self: center;
              }

              .product-price {
                align-items: center;
              }
            }
          }
        }

        .dialog-actions {
          flex-direction: column-reverse;

          button {
            width: 100%;
          }
        }
      }
    `,
  ],
})
export class OrderDetailDialogComponent implements OnInit {
  order: OrderHistory | null = null;
  loading = true;

  constructor(
    public dialogRef: MatDialogRef<OrderDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { orderId: string },
    private orderHistoryService: OrderHistoryService,
  ) {}

  ngOnInit(): void {
    this.loadOrderDetail();
  }

  private loadOrderDetail(): void {
    this.loading = true;

    this.orderHistoryService.getOrderDetail(this.data.orderId).subscribe({
      next: response => {
        this.loading = false;
        if (!response.error) {
          this.order = response.data;
        } else {
          console.error('Error loading order detail:', response.message);
        }
      },
      error: error => {
        this.loading = false;
        console.error('Error loading order detail:', error);
      },
    });
  }

  getStatusColor(status: number): string {
    return ORDER_STATUS_COLORS[status as keyof typeof ORDER_STATUS_COLORS] || '#6c757d';
  }

  getPaymentMethodText(paymentMethod: number): string {
    return this.orderHistoryService.getPaymentMethodText(paymentMethod);
  }

  formatPrice(price: number): string {
    return this.orderHistoryService.formatPrice(price);
  }

  getProductImage(product: any): string {
    if (product.thumbnail) {
      return product.thumbnail.startsWith('http')
        ? product.thumbnail
        : `http://localhost:4000/${product.thumbnail}`;
    }
    if (product.info?.thumbail) {
      return product.info.thumbail.startsWith('http')
        ? product.info.thumbail
        : `http://localhost:4000/${product.info.thumbail}`;
    }
    return '/assets/images/placeholder.jpg';
  }

  onImageError(event: any): void {
    event.target.src = '/assets/images/placeholder.jpg';
  }

  getSubtotal(): number {
    if (!this.order?.products) return 0;
    return this.order.products.reduce((sum, product) => sum + product.price * product.count, 0);
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onReorder(): void {
    // TODO: Implement reorder functionality
    console.log('Reorder:', this.order);
    this.dialogRef.close({ action: 'reorder', order: this.order });
  }
}
