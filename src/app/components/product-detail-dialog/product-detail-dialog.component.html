<div class="product-detail-dialog">
  <!-- Close button -->
  <button class="close-button" (click)="onClose()">
    <i-tabler name="x"></i-tabler>
  </button>

  <!-- Loading state -->
  <div *ngIf="loading" class="loading-container">
    <div class="spinner-border" role="status">
      <span class="visually-hidden"><PERSON>ang tải...</span>
    </div>
    <p>Đang tải thông tin sản phẩm...</p>
  </div>

  <!-- Error state -->
  <div *ngIf="error && !loading" class="error-container">
    <i class="fas fa-exclamation-triangle"></i>
    <h4>Không thể tải thông tin sản phẩm</h4>
    <button class="retry-button" (click)="loadProductDetail()">Thử lại</button>
  </div>

  <!-- Product content -->
  <div *ngIf="product && !loading && !error" class="frame-parent7">
    <section class="frame-parent13">
      <div class="t-mn-wrapper">
        <h3 class="t-mn1">CHI TIẾT SẢN PHẨM</h3>
      </div>
      <div class="frame-parent14">
        <div class="frame-parent15">
          <div class="frame-wrapper8">
            <div class="image-group">
              <!-- Main product image with hover effect -->
              <div class="product-image-container position-relative">
                <div class="image-overlay"></div>
                <img
                  class="product-image rounded product-main-image"
                  [src]="getCurrentImage()"
                  alt="Product Image"
                  (error)="handleImageError($event)" />

                <!-- Rating overlay -->
                <div class="rating-overlay position-absolute">
                  <img
                    class="rating-stars"
                    loading="lazy"
                    [src]="ratingImage"
                    alt="Rating" />
                </div>
              </div>

              <!-- Thumbnail images -->
              <div *ngIf="product.pictures && product.pictures.length > 1" class="thumbnail-container">
                <img
                  *ngFor="let picture of product.pictures; let i = index"
                  [src]="getProductImageUrl(picture)"
                  [class.active]="selectedImageIndex === i"
                  class="thumbnail-image"
                  (click)="selectImage(i)"
                  (error)="handleImageError($event)" />
              </div>
            </div>
          </div>
          <div class="frame-parent16">
            <div class="frame-parent17">
              <div class="tn-mn-n-wrapper">
                <h3 class="tn-mn-n">{{ product.name }}</h3>
              </div>
              <div class="frame-parent18">
                <div class="frame-parent19">
                  <div class="frame-wrapper9">
                    <div class="frame-parent20">
                      <div class="vn-parent">
                        <h3 class="vn">{{ formatPrice(product.price) }}</h3>
                        <div *ngIf="product.priceOld && product.priceOld > product.price" class="vn-wrapper">
                          <div class="vn1">{{ formatPrice(product.priceOld) }}</div>
                        </div>
                      </div>
                      <div class="frame-parent21">
                        <div class="view-more-button">
                          <img
                            class="frame-child13"
                            loading="lazy"
                            alt=""
                            src="assets/group-2241.svg"
                          />
                        </div>
                        <div class="nh-gi">{{ product.revenue }} đã bán</div>
                      </div>
                    </div>
                  </div>
<!--                  <div class="tiu-option">{{ product.trademark }}</div>-->
                </div>
                <div *ngIf="getDiscountPercentage()" class="frame-wrapper11">
                  <div class="rectangle-parent11">
                    <div class="frame-child14"></div>
                    <div class="option-value">{{ getDiscountPercentage() }}</div>
                  </div>
                </div>
              </div>

              <!-- Product options -->
              <div *ngIf="product.classify && product.classify.length > 0" class="frame-parent22">
                <div *ngFor="let classify of product.classify" class="classify-group">
                  <!-- <h4 class="classify-title">{{ classify.name }}</h4> -->
                  <div class="options-container">
                    <button
                      *ngFor="let option of classify.data"
                      class="group-button"
                      [class.active]="selectedOption?._id === option._id"
                      (click)="selectOption(option)">
                      <div class="frame-child15"></div>
                      <div class="op2">{{ option.name }}</div>
                      <div *ngIf="option.price" class="option-price">{{ formatOptionPrice(option.price) }}</div>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quantity selector -->
            <div class="frame-wrapper12">
              <div class="s-lng-parent">
                <div class="s-lng">Số lượng</div>
                <div class="quantity-controls">
                  <button class="control-container" (click)="updateQuantity(-1)">
                    <div class="control-container-child"></div>
                    <span class="quantity-buttons">-</span>
                  </button>
                  <input class="quantity-input" type="number" [(ngModel)]="quantity" min="1" />
                  <button class="control-container" (click)="updateQuantity(1)">
                    <div class="control-container-child"></div>
                    <div class="quantity-buttons">+</div>
                  </button>
                </div>
              </div>
            </div>

            <!-- Product description -->
            <div class="product-description">
              <div class="m-t-sn-phm-parent">
                <div class="m-t-sn">MÔ TẢ SẢN PHẨM</div>
                <div class="description-content">
                  <div *ngIf="!expandedContent" class="chng-ta-vn">
                    <div [innerHTML]="getTrimmedDescription()"></div>
                    <span *ngIf="(product.description || '').length > 100" class="read-more" (click)="expandedContent = true">...Xem thêm</span>
                  </div>
                  <div *ngIf="expandedContent" class="chng-ta-vn">
                    <div [innerHTML]="product.description"></div>
                    <div class="read-less" (click)="expandedContent = false">Thu gọn</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- View more button -->
            <div *ngIf="(product.description || '').length > 100" class="frame-wrapper13">
              <div class="view-more-button-parent">
                <div class="view-more-button" (click)="viewMore()">
                  <div class="xem-thm">{{ expandedContent ? 'Thu gọn' : 'Xem thêm' }}</div>
                </div>
                <img
                  class="arrow2-5-icon"
                  [style.transform]="expandedContent ? 'rotate(180deg)' : 'rotate(0deg)'"
                  alt=""
                  src="assets/arrow2-5.svg" />
              </div>
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="frame-wrapper14">
          <div class="vector-parent">
            <img class="vector-icon" alt="" src="assets/vector-9.svg" />
            <div class="frame-child19"></div>
            <div class="add-to-cart-actions">
              <div class="cart-actions-container">
                <button class="cart-actions" (click)="onAddToCart()">
                  <div class="cart-actions-child"></div>
                  <div class="thm-vo-gi">Thêm vào giỏ</div>
                </button>
                <button class="rectangle-parent15" (click)="onBuyNow()">
                  <div class="frame-child20"></div>
                  <div class="mua-ngay3">Mua ngay</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
