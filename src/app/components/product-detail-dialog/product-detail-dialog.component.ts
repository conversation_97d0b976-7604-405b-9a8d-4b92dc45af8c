import {
  Component,
  ViewEncapsulation,
  HostBinding,
  Inject,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NgForOf, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ApiService } from '../../core/services/api.service';
import { CartService } from '../../core/services/cart.service';
import { AuthService } from '../../core/services/auth.service';
import { environment } from '../../../environments/environment';
import { IconsModule } from '../../icons.module';
import { ProductInfo, ClassifyOption, BRAND_ID } from '../../core/models/cart.model';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

export interface ProductDetailData {
  productId: string;
  productImage?: string;
}

// Use ProductInfo from cart.model.ts instead

@Component({
  selector: 'app-product-detail-dialog',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [NgForOf, NgIf, FormsModule, IconsModule],
  templateUrl: './product-detail-dialog.component.html',
  styleUrls: ['./product-detail-dialog.component.scss'],
})
export class ProductDetailDialog implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';

  private destroy$ = new Subject<void>();

  // Product data
  product: ProductInfo | null = null;
  loading = true;
  error = false;

  // UI state
  selectedOption: ClassifyOption | null = null;
  expandedContent = false;
  quantity = 1;
  selectedImageIndex = 0;

  // Default values
  ratingImage = 'assets/group-2217.svg';

  constructor(
    private dialogRef: MatDialogRef<ProductDetailDialog>,
    @Inject(MAT_DIALOG_DATA) public data: ProductDetailData,
    private apiService: ApiService,
    private cartService: CartService,
    private authService: AuthService,
    private toastr: ToastrService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.loadProductDetail();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load product detail from API
   */
  loadProductDetail() {
    this.loading = true;
    this.error = false;

    this.apiService
      .get(`user/api/chi-tiet-san-pham/${this.data.productId}.html`, { output: 'json' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.loading = false;

          if (response && !response.error && response.data?.product) {
            this.product = response.data.product;

            // Set default selected option if classify exists
            if (this.product?.classify && this.product.classify.length > 0) {
              const firstOption = this.product.classify[0].data?.[0];
              if (firstOption) {
                this.selectedOption = firstOption;
              }
            }
          } else {
            this.error = true;
          }
        },
        error: error => {
          this.loading = false;
          this.error = true;
          console.error('Error loading product detail:', error);
        },
      });
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(image: string): string {
    if (!image) return 'assets/images/placeholder.svg';
    if (image.startsWith('http')) return image;
    return `${environment.imageApiUrl}${image}`;
  }

  /**
   * Get current product image
   */
  getCurrentImage(): string {
    if (!this.product) return this.data.productImage || 'assets/images/placeholder.svg';

    if (this.product.pictures && this.product.pictures.length > 0) {
      return this.getProductImageUrl(this.product.pictures[this.selectedImageIndex]);
    }

    return this.getProductImageUrl(this.product.thumbail);
  }

  /**
   * Select option
   */
  selectOption(option: ClassifyOption) {
    this.selectedOption = option;
  }

  /**
   * Toggle description
   */
  viewMore() {
    this.expandedContent = !this.expandedContent;
  }

  /**
   * Update quantity
   */
  updateQuantity(change: number) {
    const newQuantity = this.quantity + change;
    if (newQuantity >= 1) {
      this.quantity = newQuantity;
    }
  }

  /**
   * Select image
   */
  selectImage(index: number) {
    this.selectedImageIndex = index;
  }

  /**
   * Format price
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }

  /**
   * Format option price from string
   */
  formatOptionPrice(priceStr: string): string {
    const price = parseInt(priceStr.replace(/,/g, ''));
    return this.formatPrice(price);
  }

  /**
   * Calculate discount percentage
   */
  getDiscountPercentage(): string {
    if (!this.product?.priceOld || this.product.priceOld <= this.product.price) {
      return '';
    }
    const discount = Math.round(
      ((this.product.priceOld - this.product.price) / this.product.priceOld) * 100,
    );
    return `-${discount}%`;
  }

  /**
   * Add to cart
   */
  async onAddToCart() {
    if (!this.validateProduct()) return;

    try {
      await this.cartService.addToCart(
        this.product!._id,
        this.quantity,
        this.product!.storeId || BRAND_ID,
        this.selectedOption || undefined,
        '', // noteProduct - có thể thêm input cho ghi chú
        this.product!,
      );

      this.toastr.success('Đã thêm sản phẩm vào giỏ hàng', 'Thành công');
      this.dialogRef.close({ action: 'addToCart', success: true });
    } catch (error) {
      console.error('Error adding to cart:', error);
      this.toastr.error('Có lỗi xảy ra khi thêm vào giỏ hàng', 'Lỗi');
    }
  }

  /**
   * Buy now
   */
  async onBuyNow() {
    if (!this.validateProduct()) return;

    try {
      await this.cartService.addToCart(
        this.product!._id,
        this.quantity,
        this.product!.storeId || BRAND_ID,
        this.selectedOption || undefined,
        '', // noteProduct
        this.product!,
      );

      this.toastr.success('Đã thêm sản phẩm vào giỏ hàng', 'Thành công');
      this.dialogRef.close({ action: 'buyNow', success: true });

      // Navigate to cart page
      this.router.navigate(['/pay']);
    } catch (error) {
      console.error('Error in buy now:', error);
      this.toastr.error('Có lỗi xảy ra', 'Lỗi');
    }
  }

  /**
   * Validate product before adding to cart
   */
  private validateProduct(): boolean {
    // Check if user is logged in
    if (!this.authService.isAuthenticated()) {
      this.toastr.warning('Vui lòng đăng nhập để mua hàng', 'Yêu cầu đăng nhập');
      this.dialogRef.close({ action: 'login' });
      return false;
    }

    // Check if product has classify and option is selected
    if (this.product?.classify && this.product.classify.length > 0 && !this.selectedOption) {
      this.toastr.warning('Vui lòng chọn thuộc tính sản phẩm', 'Thiếu thông tin');
      return false;
    }

    // Check product weight (if required)
    if (!this.product?.weight) {
      this.toastr.error('Sản phẩm không có thông tin trọng lượng', 'Lỗi sản phẩm');
      return false;
    }

    return true;
  }

  /**
   * Close dialog
   */
  onClose() {
    this.dialogRef.close();
  }

  /**
   * Handle image error
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/images/placeholder.svg';
    imgElement.classList.add('img-error');
  }

  /**
   * Get safely trimmed HTML description
   */
  getTrimmedDescription(): string {
    if (!this.product?.description) return '';

    // Create a temporary div to parse HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = this.product.description;

    // Get text content only (without HTML tags)
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Trim to 100 characters
    if (textContent.length <= 100) {
      return this.product.description;
    }

    // For longer content, we need to return a safe HTML snippet
    // This is a simplified version - in production you might want a more robust HTML parser
    return this.product.description.substring(0, 100) + '...';
  }
}
