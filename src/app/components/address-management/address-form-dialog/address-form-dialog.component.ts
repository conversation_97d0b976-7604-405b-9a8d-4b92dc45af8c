import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TablerIconsModule } from 'angular-tabler-icons';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { AuthService } from '../../../core/services/auth.service';
import {
  Address,
  AddressFormData,
  Province,
  District,
  Ward,
  AddressHelper,
} from '../../../core/models/address.model';

export interface AddressFormDialogData {
  mode: 'add' | 'edit';
  address?: Address;
}

@Component({
  selector: 'app-address-form-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatCheckboxModule,
    TablerIconsModule,
  ],
  template: `
    <div class="address-form-dialog">
      <h2 mat-dialog-title>
        <i-tabler [name]="data.mode === 'add' ? 'plus' : 'edit'" class="me-2"></i-tabler>
        {{ data.mode === 'add' ? 'Thêm địa chỉ mới' : 'Chỉnh sửa địa chỉ' }}
      </h2>

      <mat-dialog-content>
        <form [formGroup]="addressForm" class="address-form">
          <!-- Name and Phone -->
          <div class="row">
            <div class="col-md-6">
              <mat-label>Họ và tên</mat-label>
              <mat-form-field appearance="outline" class="w-100">
                <input matInput formControlName="name" placeholder="Nhập họ và tên" />
                <mat-error *ngIf="addressForm.get('name')?.hasError('required')">
                  Vui lòng nhập họ và tên
                </mat-error>
                <mat-error *ngIf="addressForm.get('name')?.hasError('minlength')">
                  Tên phải có ít nhất 2 ký tự
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-label>Số điện thoại</mat-label>
              <mat-form-field appearance="outline" class="w-100">
                <input matInput formControlName="phone" placeholder="Nhập số điện thoại" />
                <mat-error *ngIf="addressForm.get('phone')?.hasError('required')">
                  Vui lòng nhập số điện thoại
                </mat-error>
                <mat-error *ngIf="addressForm.get('phone')?.hasError('pattern')">
                  Số điện thoại không đúng định dạng
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Location selects -->
          <div class="row">
            <div class="col-md-4">
              <mat-label>Tỉnh/Thành phố</mat-label>
              <mat-form-field appearance="outline" class="w-100">
                <mat-select
                  formControlName="province"
                  (selectionChange)="onProvinceChange($event.value)"
                >
                  <mat-option *ngFor="let province of provinces" [value]="province.name">
                    {{ province.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="addressForm.get('province')?.hasError('required')">
                  Vui lòng chọn tỉnh/thành phố
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-md-4">
              <mat-label>Quận/Huyện</mat-label>
              <mat-form-field appearance="outline" class="w-100">
                <mat-select
                  formControlName="district"
                  (selectionChange)="onDistrictChange($event.value)"
                  [disabled]="!selectedProvince"
                >
                  <mat-option *ngFor="let district of districts" [value]="district.name">
                    {{ district.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="addressForm.get('district')?.hasError('required')">
                  Vui lòng chọn quận/huyện
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-md-4">
              <mat-label>Phường/Xã</mat-label>
              <mat-form-field appearance="outline" class="w-100">
                <mat-select formControlName="ward" [disabled]="!selectedDistrict">
                  <mat-option *ngFor="let ward of wards" [value]="ward.name">
                    {{ ward.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="addressForm.get('ward')?.hasError('required')">
                  Vui lòng chọn phường/xã
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Street address -->
                       <mat-label>Địa chỉ cụ thể</mat-label>
          <mat-form-field appearance="outline" class="w-100">
            <textarea
              matInput
              formControlName="street"
              rows="2"
              placeholder="Số nhà, tên đường..."
            ></textarea>
            <mat-error *ngIf="addressForm.get('street')?.hasError('required')">
              Vui lòng nhập địa chỉ cụ thể
            </mat-error>
            <mat-error *ngIf="addressForm.get('street')?.hasError('minlength')">
              Địa chỉ phải có ít nhất 5 ký tự
            </mat-error>
          </mat-form-field>

          <!-- Default checkbox -->
          <div class="default-checkbox">
            <mat-checkbox formControlName="default"> Đặt làm địa chỉ mặc định </mat-checkbox>
          </div>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()">Hủy</button>
        <button
          mat-raised-button
          color="primary"
          (click)="onSave()"
          [disabled]="addressForm.invalid || saving"
        >
          <span *ngIf="saving" class="spinner-border spinner-border-sm me-2"></span>
          {{ data.mode === 'add' ? 'Thêm' : 'Cập nhật' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [
    `
      .address-form-dialog {
        min-width: 500px;
      }

      .address-form {
        padding: 16px 0;
      }

      .default-checkbox {
        margin: 16px 0;
      }

      mat-form-field {
        margin-bottom: 16px;
      }

      .spinner-border-sm {
        width: 1rem;
        height: 1rem;
      }

      @media (max-width: 768px) {
        .address-form-dialog {
          min-width: unset;
          width: 100%;
        }
      }
    `,
  ],
})
export class AddressFormDialogComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  addressForm: FormGroup;
  provinces: Province[] = [];
  districts: District[] = [];
  wards: Ward[] = [];

  selectedProvince: Province | null = null;
  selectedDistrict: District | null = null;

  saving = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<AddressFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddressFormDialogData,
  ) {
    this.addressForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadProvinces();

    if (this.data.mode === 'edit' && this.data.address) {
      this.populateForm(this.data.address);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9+\-\s()]+$/)]],
      province: ['', Validators.required],
      district: ['', Validators.required],
      ward: ['', Validators.required],
      street: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(200)]],
      default: [false],
    });
  }

  /**
   * Load provinces
   */
  private loadProvinces(): void {
    this.authService
      .getProvinces()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: provinces => {
          this.provinces = provinces;
        },
        error: error => {
          console.error('Error loading provinces:', error);
          this.snackBar.open('Có lỗi xảy ra khi tải danh sách tỉnh/thành phố', 'Đóng', {
            duration: 3000,
          });
        },
      });
  }

  /**
   * Handle province change
   */
  onProvinceChange(provinceName: string): void {
    this.selectedProvince = this.provinces.find(p => p.name === provinceName) || null;

    // Reset district and ward
    this.addressForm.patchValue({
      district: '',
      ward: '',
    });
    this.districts = [];
    this.wards = [];
    this.selectedDistrict = null;

    if (this.selectedProvince) {
      this.loadDistricts(this.selectedProvince.code);
    }
  }

  /**
   * Load districts by province
   */
  private loadDistricts(provinceCode: string): void {
    this.authService
      .getDistricts(provinceCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: districts => {
          this.districts = districts;
        },
        error: error => {
          console.error('Error loading districts:', error);
          this.snackBar.open('Có lỗi xảy ra khi tải danh sách quận/huyện', 'Đóng', {
            duration: 3000,
          });
        },
      });
  }

  /**
   * Handle district change
   */
  onDistrictChange(districtName: string): void {
    this.selectedDistrict = this.districts.find(d => d.name === districtName) || null;

    // Reset ward
    this.addressForm.patchValue({
      ward: '',
    });
    this.wards = [];

    if (this.selectedDistrict) {
      this.loadWards(this.selectedDistrict.code);
    }
  }

  /**
   * Load wards by district
   */
  private loadWards(districtCode: string): void {
    this.authService
      .getWards(districtCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: wards => {
          this.wards = wards;
        },
        error: error => {
          console.error('Error loading wards:', error);
          this.snackBar.open('Có lỗi xảy ra khi tải danh sách phường/xã', 'Đóng', {
            duration: 3000,
          });
        },
      });
  }

  /**
   * Populate form with existing address data
   */
  private populateForm(address: Address): void {
    this.addressForm.patchValue({
      name: address.name,
      phone: address.phone,
      province: address.province,
      district: address.district,
      ward: address.ward,
      street: address.street,
      default: address.default,
    });

    // Load districts and wards for existing address
    const province = this.provinces.find(p => p.name === address.province);
    if (province) {
      this.selectedProvince = province;
      this.loadDistricts(province.code);

      // After districts load, select district and load wards
      setTimeout(() => {
        const district = this.districts.find(d => d.name === address.district);
        if (district) {
          this.selectedDistrict = district;
          this.loadWards(district.code);
        }
      }, 500);
    }
  }

  /**
   * Save address
   */
  async onSave(): Promise<void> {
    if (this.addressForm.invalid) {
      this.addressForm.markAllAsTouched();
      return;
    }

    this.saving = true;

    try {
      const formData: AddressFormData = this.addressForm.value;
      let result;

      if (this.data.mode === 'add') {
        result = await this.authService.addAddress(formData);
      } else if (this.data.address?._id) {
        result = await this.authService.updateAddress(this.data.address._id, formData);
      } else {
        throw new Error('Invalid address ID for update');
      }

      if (result.success) {
        this.snackBar.open(result.message, 'Đóng', { duration: 3000 });
        this.dialogRef.close(true);
      } else {
        this.snackBar.open(result.message, 'Đóng', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error saving address:', error);
      this.snackBar.open('Có lỗi xảy ra khi lưu địa chỉ', 'Đóng', { duration: 3000 });
    } finally {
      this.saving = false;
    }
  }

  /**
   * Cancel dialog
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }
}
