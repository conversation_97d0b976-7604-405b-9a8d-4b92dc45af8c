import { Component, ViewEncapsulation, HostBinding, OnInit, OnDestroy } from '@angular/core';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DropdownMenuComponent } from '../../dropdown-menu/dropdown-menu.component';
import { BookingDialogComponent } from '../../shared/components/booking-dialog/booking-dialog.component';
import { DialogHelperService } from '../../shared/services/dialog-helper.service';
import { IconsModule } from '../../icons.module';
import { CartService } from '../../core/services/cart.service';

@Component({
  selector: 'app-navbar',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [DropdownMenuComponent, RouterModule, CommonModule, IconsModule],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';
  isMobileMenuOpen = false;
  cartItemsCount = 0;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    public router: Router,
    private dialogHelper: DialogHelperService,
    private cartService: CartService,
  ) {}

  ngOnInit() {
    // Check if URL has booking parameter
    this.route.queryParams.subscribe(params => {
      if (params['booking'] !== undefined) {
        // Auto-trigger booking dialog
        this.onBookingClick();
      }
    });

    // Subscribe to cart changes
    this.cartService.cartSummary$.pipe(takeUntil(this.destroy$)).subscribe(summary => {
      this.cartItemsCount = summary.totalCount;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onMENUTextClick() {
    this.router.navigate(['/menu']);
  }

  onBookingClick() {
    this.dialogHelper.openBookingDialog(BookingDialogComponent).subscribe(result => {
      if (result) {
        console.log('Booking created:', result);
        // Có thể thêm logic xử lý sau khi đặt bàn thành công
      }
    });
  }

  onPayment() {
    this.router.navigate(['/pay']);
  }

  // Mobile menu functionality
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }
}
