.bg-header {
  background-color: var(--color-steelblue-300);
}

/* Container cho navbar */
.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 19px 0;
  width: 100%;
}

/* Logo container */
.logo-container {
  flex: 0 0 auto;
  position: absolute;
  top: 27px;
}

/* <PERSON>u ch<PERSON>h */
.menu-wrapper {
  display: flex;
  gap: var(--gap-20);
  align-items: center;
  justify-content: center;
  flex: 1 1 auto;
  padding: 0;
}

/* Right menu container */
.right-menu {
  display: flex;
  align-items: center;
  gap: var(--gap-10);
  flex: 0 0 auto;
}

/* Container cho các icon */
.icons-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Style cho các menu item */
.menu {
  position: relative;
  color: var(--color-white);
  cursor: pointer;
  z-index: 1;
  font-weight: 500;
}

.frame-child5 {
  height: 32px;
  width: 161px;
  position: relative;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  display: none;
}
.image-579-icon {
  width: 20px;
  position: relative;
  max-height: 100%;
  object-fit: cover;
  z-index: 2;
}
.t-bn-ngay {
  position: relative;
  font-size: var(--font-size-14);
  font-family: var(--font-varela);
  color: var(--color-white);
  text-align: left;
  z-index: 2;
}
.frame-wrapper3,
.rectangle-parent3 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.rectangle-parent3 {
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  flex-direction: row;
  padding: var(--padding-6) 9px var(--padding-6) var(--padding-14);
  gap: var(--gap-10);
  z-index: 1;
}
.frame-wrapper3 {
  cursor: pointer;
  border: 0;
  padding: var(--padding-5) 9px 0 0;
  background-color: transparent;
  flex-direction: column;
}
.bell-notification-1-icon {
  width: 24px;
  height: 24px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 1;
}
.bell-notification-1-wrapper,
.shopping-cart-1-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-12) 0 0;
}
.shopping-cart-1-wrapper {
  padding: var(--padding-11) var(--padding-6) 0 0;
}

// Auth links styles
.user-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-11) var(--padding-6) 0 0;

  .auth-links {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    z-index: 1;

    .auth-link {
      position: relative;
      font-size: var(--font-size-14);
      font-family: var(--font-varela);
      color: var(--color-white);
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: var(--color-steelblue-100);
        text-decoration: underline;
      }
    }

    .separator {
      color: var(--color-white);
      font-size: var(--font-size-14);
    }
  }
}
.frame-child6 {
  height: 40px;
  width: 81px;
  position: relative;
  border-radius: var(--br-100);
  border: 1px solid var(--color-steelblue-100);
  box-sizing: border-box;
  display: none;
}
.image-541-icon {
  width: 20px;
  height: 20px;
  position: relative;
  object-fit: cover;
  z-index: 2;
}
.image-541-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.image-533-icon {
  height: 32px;
  width: 32px;
  position: relative;
  object-fit: cover;
  z-index: 2;
}
.cho-hai-san-child,
.frame-header {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
}

.cho-hai-san-child,
.frame-header {
  max-width: 100%;
}
.frame-header {
  flex: 1;
  padding: var(--padding-16) 125px var(--padding-19) 439px;
  gap: var(--gap-14);
  top: 0;
  z-index: 99;
  position: sticky;
  text-align: left;
  font-size: var(--font-size-14);
  color: var(--color-white);
  font-family: var(--font-varela);
}
@media screen and (max-width: 750px) {
  .trang-ch-parent {
    display: none;
  }
  .frame-header {
    padding-left: 219px;
    padding-right: 62px;
    box-sizing: border-box;
  }
}
@media screen and (max-width: 450px) {
  .frame-header {
    padding-left: var(--padding-20);
    padding-right: var(--padding-20);
    box-sizing: border-box;
  }
  .cho-hai-san-child {
    padding-bottom: 211px;
    box-sizing: border-box;
  }
}
.logo-white {
  width: 100px;
  height: 100px;
  object-fit: contain;
  z-index: 2;
  display: block;
}

/* Mobile Menu Styles */
.mobile-navbar {
  width: 100%;
  height: 80px;
}

.mobile-navbar .logo-white {
  width: 60px;
  height: 60px;
}

/* Specific styling for the mobile navigation items */
.mobile-navbar {
  position: relative;
}

.mobile-navbar .logo-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: center;
  z-index: 1;
}

.mobile-left,
.mobile-right {
  display: flex;
  align-items: center;
  z-index: 10;
  padding: 0 5px;
}

/* Mobile menu dropdown */
.mobile-menu {
  position: fixed;
  top: 80px; /* Adjust based on your navbar height */
  left: 0;
  right: 0;
  background-color: var(--color-steelblue-300);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-150%);
  transition: transform 0.3s ease;
  z-index: 100;
}

.show-mobile-menu {
  transform: translateY(0);
}

.mobile-menu-item {
  padding: 15px 0;
  color: var(--color-white);
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

.mobile-booking-btn {
  background-color: var(--color-steelblue-100);
  color: var(--color-white);
  border: none;
  border-radius: var(--br-5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-icon {
  width: 20px;
  height: 20px;
}

/* Menu icon styling */
.menu-icon {
  width: 28px;
  height: 28px;
  color: var(--color-white);
  cursor: pointer;
}
.shopping-cart-1-wrapper, .bell-notification-1-wrapper {
  cursor: pointer;
}