<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h2><PERSON><PERSON><PERSON></h2>
      <p><PERSON><PERSON><PERSON> nhập để truy cập v<PERSON><PERSON> hệ thống</p>
    </div>
    
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <app-text-input
        formControlName="phone"
        label="Số điện thoại"
        type="tel"
        placeholder="Nhập số điện thoại của bạn"
        [required]="true"
        [errorMessages]="{
          required: 'Số điện thoại là bắt buộc',
          pattern: 'Số điện thoại không đúng định dạng'
        }"
      ></app-text-input>

      <app-text-input
        formControlName="password"
        label="Mật khẩu"
        type="password"
        placeholder="Nhập mật khẩu"
        [required]="true"
        [errorMessages]="{
          required: '<PERSON><PERSON><PERSON> khẩu là bắt buộc'
        }"
      ></app-text-input>

      <div class="form-options">
        <app-checkbox-input
          formControlName="rememberMe"
          label="Ghi nhớ đăng nhập"
          color="primary"
        ></app-checkbox-input>
        <a routerLink="/quen-mat-khau" class="forgot-password">Quên mật khẩu?</a>
      </div>

      <button mat-raised-button color="primary" type="submit" class="full-width login-button" [disabled]="loginForm.invalid">Đăng Nhập</button>
      
      <div class="register-link">
        <span>Chưa có tài khoản? </span>
        <a routerLink="/dang-ky">Đăng ký ngay</a>
      </div>
    </form>
  </div>
</div>
