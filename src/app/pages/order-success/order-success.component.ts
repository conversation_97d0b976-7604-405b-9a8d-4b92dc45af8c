import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { IconsModule } from '../../icons.module';

@Component({
  selector: 'app-order-success',
  standalone: true,
  imports: [CommonModule, IconsModule],
  template: `
    <div class="order-success-container">
      <div class="success-content">
        <div class="success-icon">
          <i-tabler name="check-circle" class="success-check"></i-tabler>
        </div>
        
        <h1 class="success-title">Đặt hàng thành công!</h1>
        
        <div class="order-info" *ngIf="orderId">
          <p class="order-id">Mã đơn hàng: <strong>{{ orderId }}</strong></p>
          <p class="order-message">
            Cảm ơn bạn đã đặt hàng. Chúng tôi sẽ liên hệ với bạn sớm nhất để xác nhận đơn hàng.
          </p>
        </div>
        
        <div class="action-buttons">
          <button class="btn btn-primary" (click)="goToHome()">
            Về trang chủ
          </button>
          <button class="btn btn-secondary" (click)="goToOrders()">
            Xem đơn hàng
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .order-success-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 80vh;
      padding: 20px;
    }

    .success-content {
      text-align: center;
      max-width: 500px;
      padding: 40px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .success-icon {
      margin-bottom: 24px;
    }

    .success-check {
      font-size: 64px;
      color: #10b981;
    }

    .success-title {
      font-size: 28px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 24px;
    }

    .order-info {
      margin-bottom: 32px;
      padding: 20px;
      background: #f9fafb;
      border-radius: 8px;
    }

    .order-id {
      font-size: 16px;
      color: #374151;
      margin-bottom: 12px;
    }

    .order-message {
      font-size: 14px;
      color: #6b7280;
      line-height: 1.5;
      margin: 0;
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      border: none;
      cursor: pointer;
      transition: all 0.2s;
      min-width: 120px;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
    }

    .btn-primary:hover {
      background: #2563eb;
    }

    .btn-secondary {
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
    }

    .btn-secondary:hover {
      background: #e5e7eb;
    }

    @media (max-width: 640px) {
      .success-content {
        padding: 24px;
      }

      .action-buttons {
        flex-direction: column;
      }

      .btn {
        width: 100%;
      }
    }
  `]
})
export class OrderSuccessComponent implements OnInit {
  orderId: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.orderId = params['orderId'] || null;
    });
  }

  goToHome(): void {
    this.router.navigate(['/']);
  }

  goToOrders(): void {
    this.router.navigate(['/orders']);
  }
}
