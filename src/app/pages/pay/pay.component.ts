import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { RouterModule } from '@angular/router';
import { IconsModule } from '../../icons.module';

import { Subject, firstValueFrom } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CartService } from '../../core/services/cart.service';
import { OrderService } from '../../core/services/order.service';
import { AuthService } from '../../core/services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import {
  CartItem,
  ShopCart,
  BranchStore,
  CustomerInfo,
  PaymentMethod,
  BankInfo,
  PAYMENT_METHODS,
  BANKS,
} from '../../core/models/cart.model';
import { LoadingService } from 'src/app/components/loading/loading.service';
import { environment } from 'src/environments/environment';
import { BookingService } from 'src/app/core/services/booking.service';

// Interfaces are now imported from cart.model.ts

@Component({
  selector: 'app-pay',
  standalone: true,
  imports: [CommonModule, FormsModule, MatCheckboxModule, RouterModule, IconsModule],
  templateUrl: './pay.component.html',
  styleUrls: ['./pay.component.scss'],
})
export class PayComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  loading = false;
  loadingPayment = false;

  // Form data
  selectedStoreIndex = 0;
  selectedCustomerIndex = 0;
  selectedPaymentIndex = 1; // Default to cash payment
  selectedBankIndex = 0;
  discountCode = '';
  discountAmount = 0;

  // Data
  cartItems: CartItem[] = [];
  shopCarts: ShopCart[] = [];
  stores: any[] = [];
  customerInfos: CustomerInfo[] = [];
  paymentMethods: PaymentMethod[] = PAYMENT_METHODS;
  banks: BankInfo[] = BANKS;

  // Summary
  subtotal = 0;
  totalAmount = 0;

  constructor(
    private cartService: CartService,
    private orderService: OrderService,
    private bookingService: BookingService,
    private authService: AuthService,
    private toastr: ToastrService,
    private router: Router,
    private loadingService: LoadingService,
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
    this.subscribeToCartChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load initial data
   */
  private async loadInitialData(): Promise<void> {
    this.loading = true;
    this.loadingService.show();

    try {
      // Load cart items
      await this.loadCartItems();

      // Load branch stores
      await this.loadBranchStores();

      // Load customer info from address list API
      await this.loadCustomerInfo();

      this.calculateTotals();
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.toastr.error('Có lỗi xảy ra khi tải dữ liệu', 'Lỗi');
    } finally {
      this.loading = false;
      this.loadingService.hide();
    }
  }

  /**
   * Subscribe to cart changes
   */
  private subscribeToCartChanges(): void {
    this.cartService.cart$.pipe(takeUntil(this.destroy$)).subscribe(items => {
      this.cartItems = items.filter(item => item.selected);
      this.calculateTotals();
    });
  }

  /**
   * Load cart items from service
   */
  private async loadCartItems(): Promise<void> {
    try {
      this.shopCarts = await this.cartService.fetchCartInformation();

      // Flatten shop carts to cart items
      this.cartItems = [];
      this.shopCarts.forEach(shop => {
        this.cartItems.push(...shop.products.filter(item => item.selected));
      });
    } catch (error) {
      console.error('Error loading cart items:', error);
    }
  }

  /**
   * Load branch stores
   */
  private async loadBranchStores(): Promise<void> {
    try {
      const response = await firstValueFrom(this.bookingService.getBranches());
      console.log('🏪 Loaded branch stores:', response);

      if (response && Array.isArray(response)) {
        this.stores = response;
        console.log('✅ Stores loaded:', this.stores.length, 'stores');
      } else if (response && (response as any).data && Array.isArray((response as any).data)) {
        this.stores = (response as any).data;
        console.log('✅ Stores loaded from data:', this.stores.length, 'stores');
      } else {
        console.warn('⚠️ No stores data found in response');
        this.stores = [];
      }
    } catch (error) {
      console.error('❌ Error loading branch stores:', error);
      this.stores = [];
    }
  }

  /**
   * Load customer info from address list API
   */
  private async loadCustomerInfo(): Promise<void> {
    try {
      const user = this.authService.currentUserValue;

      if (!user) {
        console.warn('No user logged in');
        this.customerInfos = [];
        return;
      }

      // Load address list from API
      const addressList = await firstValueFrom(this.authService.getAddressList());
      console.log('📍 Loaded address list:', addressList);

      if (addressList && addressList.length > 0) {
        // Convert addresses to customer info format
        this.customerInfos = addressList.map(address => ({
          _id: address._id || '',
          name: address.name,
          phone: address.phone,
          address: address.address, // Full address string
          province: address.province,
          district: address.district,
          ward: address.ward,
          street: address.street,
          default: address.default,
        }));

        // Sort to put default address first
        this.customerInfos.sort((a, b) => {
          if (a.default && !b.default) return -1;
          if (!a.default && b.default) return 1;
          return 0;
        });

        console.log('✅ Customer infos loaded:', this.customerInfos.length, 'addresses');
      } else {
        // Fallback to user info if no addresses
        console.warn('⚠️ No addresses found, using user info as fallback');
        this.customerInfos = [
          {
            _id: user._id,
            name: user.fullName || 'Khách hàng',
            phone: user.phone || '',
            address: user.address || '',
            province: '',
            district: '',
            ward: '',
            street: '',
            default: true,
          },
        ];
      }
    } catch (error) {
      console.error('❌ Error loading customer info:', error);

      // Fallback to user info on error
      const user = this.authService.currentUserValue;
      this.customerInfos = [
        {
          _id: user?._id || '',
          name: user?.fullName || 'Khách hàng',
          phone: user?.phone || '',
          address: user?.address || '',
          province: '',
          district: '',
          ward: '',
          street: '',
          default: true,
        },
      ];
    }
  }

  /**
   * Calculate totals
   */
  private calculateTotals(): void {
    this.subtotal = this.cartItems.reduce((total, item) => {
      const price = item.classifyActive?.price
        ? parseInt(item.classifyActive.price.replace(/,/g, ''))
        : item.info?.price || 0;
      return total + price * item.count;
    }, 0);

    this.totalAmount = this.subtotal - this.discountAmount;
  }

  /**
   * Handle image error
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/images/placeholder.svg';
    imgElement.classList.add('img-error');
  }

  /**
   * Increase quantity
   */
  increaseQuantity(index: number): void {
    const item = this.cartItems[index];
    this.cartService.updateQuantity(item.productId, item.count + 1);
  }

  /**
   * Decrease quantity
   */
  decreaseQuantity(index: number): void {
    const item = this.cartItems[index];
    if (item.count > 1) {
      this.cartService.updateQuantity(item.productId, item.count - 1);
    }
  }

  /**
   * Remove item from cart
   */
  removeItem(index: number): void {
    const item = this.cartItems[index];
    this.cartService.removeFromCart(item.productId);
  }

  /**
   * Apply discount code
   */
  async applyDiscountCode(): Promise<void> {
    if (!this.discountCode.trim()) {
      this.toastr.warning('Vui lòng nhập mã giảm giá', 'Thiếu thông tin');
      return;
    }

    try {
      const response = await firstValueFrom(
        this.orderService.checkCoupon(
          this.discountCode,
          this.subtotal,
          this.stores[this.selectedStoreIndex]?.storeId || '',
          this.authService.currentUserValue?._id || '',
        ),
      );

      if (response && !response.error && response.data) {
        const couponData = response.data;

        if (couponData.rsCoupon.error) {
          this.toastr.error(couponData.rsCoupon.msg, 'Mã giảm giá không hợp lệ');
          this.discountAmount = 0;
        } else {
          this.discountAmount = couponData.discount;
          this.toastr.success(
            `Áp dụng mã giảm giá thành công: -${this.formatPrice(this.discountAmount)}`,
            'Thành công',
          );
        }

        this.calculateTotals();
      }
    } catch (error) {
      console.error('Error checking coupon:', error);
      this.toastr.error('Có lỗi xảy ra khi kiểm tra mã giảm giá', 'Lỗi');
    }
  }

  /**
   * Format price
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }

  /**
   * Get item price
   */
  getItemPrice(item: CartItem): number {
    return item.classifyActive?.price
      ? parseInt(item.classifyActive.price.replace(/,/g, ''))
      : item.info?.price || 0;
  }

  /**
   * Get item total price
   */
  getItemTotalPrice(item: CartItem): number {
    return this.getItemPrice(item) * item.count;
  }

  /**
   * Get item old price safely
   */
  getItemOldPrice(item: CartItem): number {
    return item.info?.priceOld || 0;
  }

  /**
   * Get selected payment method
   */
  getSelectedPaymentMethod(): PaymentMethod {
    return this.paymentMethods[this.selectedPaymentIndex];
  }

  /**
   * Get selected store
   */
  getSelectedStore(): BranchStore {
    return this.stores[this.selectedStoreIndex];
  }

  /**
   * Get selected customer
   */
  getSelectedCustomer(): CustomerInfo {
    return this.customerInfos[this.selectedCustomerIndex];
  }

  /**
   * Validate order before payment
   */
  private validateOrder(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.authService.currentUserValue) {
      errors.push('Vui lòng đăng nhập để đặt hàng');
    }

    if (this.cartItems.length === 0) {
      errors.push('Giỏ hàng trống');
    }

    if (!this.stores[this.selectedStoreIndex]) {
      errors.push('Vui lòng chọn cửa hàng');
    }

    const selectedCustomer = this.customerInfos[this.selectedCustomerIndex];
    if (!selectedCustomer) {
      errors.push('Vui lòng chọn thông tin khách hàng');
    } else {
      // Validate required fields according to backend validation
      if (!selectedCustomer.name || selectedCustomer.name.trim().length < 1) {
        errors.push('Tên khách hàng không được để trống');
      }

      if (!selectedCustomer.phone || selectedCustomer.phone.trim().length < 1) {
        errors.push('Số điện thoại không được để trống');
      }

      if (!selectedCustomer.province || selectedCustomer.province.trim().length < 1) {
        errors.push('Vui lòng chọn tỉnh/thành phố');
      }

      if (!selectedCustomer.district || selectedCustomer.district.trim().length < 1) {
        errors.push('Vui lòng chọn quận/huyện');
      }

      if (!selectedCustomer.ward || selectedCustomer.ward.trim().length < 1) {
        errors.push('Vui lòng chọn phường/xã');
      }

      if (!selectedCustomer.street || selectedCustomer.street.trim().length < 1) {
        errors.push('Vui lòng nhập địa chỉ cụ thể');
      }
    }

    if (this.selectedPaymentIndex === 0 && this.selectedBankIndex < 0) {
      errors.push('Vui lòng chọn ngân hàng thanh toán');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Process payment
   */
  async processPayment(): Promise<void> {
    const validation = this.validateOrder();

    if (!validation.valid) {
      validation.errors.forEach(error => {
        this.toastr.error(error, 'Lỗi');
      });
      return;
    }

    this.loadingPayment = true;

    try {
      const selectedStore = this.getSelectedStore();
      const selectedCustomer = this.getSelectedCustomer();
      const selectedPayment = this.getSelectedPaymentMethod();

      if (selectedPayment.type === 'cash') {
        await this.processCashPayment(selectedStore, selectedCustomer);
      } else {
        await this.processBankPayment(selectedStore, selectedCustomer);
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      this.toastr.error('Có lỗi xảy ra khi thanh toán', 'Lỗi');
    } finally {
      this.loadingPayment = false;
    }
  }

  /**
   * Process cash payment
   */
  private async processCashPayment(store: BranchStore, customer: CustomerInfo): Promise<void> {
    const orderData = this.orderService.prepareOrderData(
      this.cartItems,
      customer,
      store,
      this.discountCode,
      this.totalAmount,
    );

    const result = await this.orderService.processCashPayment(orderData);

    if (result.success) {
      this.toastr.success(result.message, 'Thành công');

      // Remove purchased items from cart
      this.cartService.removePurchasedItems(this.cartItems);

      // Navigate to success page or order history
      this.router.navigate(['/order-success'], {
        queryParams: { orderId: result.orderId },
      });
    } else {
      this.toastr.error(result.message, 'Lỗi');
    }
  }

  /**
   * Process bank payment
   */
  private async processBankPayment(store: BranchStore, customer: CustomerInfo): Promise<void> {
    const selectedBank = this.banks[this.selectedBankIndex];

    const orderData = this.orderService.prepareOrderDataForBank(
      this.cartItems,
      customer,
      store,
      selectedBank.code,
      this.discountCode,
      this.totalAmount,
    );

    const result = await this.orderService.processBankPayment(orderData);

    if (result.success && result.vnpUrl) {
      // Remove purchased items from cart
      this.cartService.removePurchasedItems(this.cartItems);

      // Redirect to VNPay
      window.location.href = result.vnpUrl;
    } else {
      this.toastr.error(result.message, 'Lỗi');
    }
  }

  getProductImageUrl(image: string): string {
    if (!image) return 'assets/images/placeholder.svg';
    if (image.startsWith('http')) return image;
    return `${environment.imageApiUrl}${image}`;
  }
}
