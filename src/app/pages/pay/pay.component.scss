// ===== MAT-CHECKBOX GLOBAL STYLING =====
::ng-deep .mat-mdc-checkbox {
  .mdc-checkbox {
    .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
    .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
      background-color: #0d6efd !important;
      border-color: #0d6efd !important;
    }

    .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background {
      border-color: #dee2e6;
    }

    .mdc-checkbox__native-control:enabled:hover ~ .mdc-checkbox__background {
      border-color: #0d6efd;
    }
  }

  .mdc-checkbox__checkmark {
    color: #ffffff !important;
  }
}

.pay-container {
  width: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding-block: var(--padding-100);
  box-sizing: border-box;
  min-height: 100vh;
}
.button-change {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: var(--bs-blue);
  background: white;
  border-radius: 4px;
  padding: 13px 30px;
  border:  1px solid var(--bs-blue);
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #0d6efd;
  }
}

.pay-content {
  display: flex;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  @media (max-width: 992px) {
    flex-direction: column;
    gap: 1.5rem;
  }
}

// ===== LEFT SECTION =====
.left-section {
  flex: 2;

  .pay-section-header {
    margin-bottom: 1.5rem;

    .pay-section-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
}

// ===== STORE SELECTION =====
.store-selection {
  margin-bottom: 1.5rem;

  .store-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    border-radius: 8px;

    .store-checkbox {
      margin-top: 0.25rem;
      margin-right: 4px;

      ::ng-deep .mat-mdc-checkbox {
        .mdc-checkbox {
          .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
            background-color: #0d6efd;
            border-color: #0d6efd;
          }
        }
      }
    }

    .store-info {
      flex: 1;

      .store-name {
        font-weight: 600;
        color: #0d6efd;
        margin-bottom: 0.5rem;
      }

      .store-address {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 0.9rem;

        .address-icon {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
}

// ===== CART ITEMS =====
.cart-items {
  margin-bottom: 2rem;

  .cart-item {
    margin-bottom: 1rem;

    .item-container {
      //border: 1px solid #e9ecef;
      border: 1px solid var(--color-gainsboro-100);

      border-radius: 8px;
      background-color: #ffffff;
      overflow: hidden;

      .item-header {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;

        .item-checkbox {
          margin-top: 0.25rem;

          ::ng-deep .mat-mdc-checkbox {
            .mdc-checkbox {
              .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
                background-color: #0d6efd;
                border-color: #0d6efd;
              }
            }
          }
        }

        .item-image {
          img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 6px;
            background-color: #f8f9fa;

            &.img-error {
              opacity: 0.7;
              filter: grayscale(20%);
            }
          }
        }

        .item-details {
          flex: 1;

          .item-name {
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.5rem;
          }

          .item-price {
            margin-bottom: 0.5rem;

            .current-price {
              color: #dc3545;
              font-weight: 600;
              margin-right: 0.5rem;
            }

            .old-price {
              color: #6c757d;
              text-decoration: line-through;
              font-size: 0.9rem;
            }
          }

          .item-option {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
          }

          .item-quantity-section {
            display: flex;
            align-items: center;
            gap: 1rem;

            .quantity-label {
              color: #6c757d;
              font-size: 0.9rem;
            }

            .quantity-controls {
              display: flex;
              align-items: center;
              gap: 0.5rem;

              .quantity-btn {
                width: 28px;
                height: 28px;
                border: 1px solid #dee2e6;
                background-color: #ffffff;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-weight: 600;

                &:hover {
                  background-color: #f8f9fa;
                }

                &:disabled {
                  opacity: 0.5;
                  cursor: not-allowed;
                }
              }

              .quantity-value {
                min-width: 24px;
                text-align: center;
                font-weight: 600;
              }
            }
          }
        }

        .item-actions {
          display: flex;
          align-items: flex-start;

          .remove-btn {
            width: 20px;
            height: 20px;
            border: none;
            background-color: transparent;
            color: #dc3545;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
              background-color: #f8f9fa;
            }
          }
        }
      }

      .item-note {
        padding: 0.75rem 1rem;
        background-color: white;
        color: #6c757d;
        font-size: 0.9rem;
      }
    }
  }
}

// ===== CART ITEMS =====
.cart-items {
  .cart-item {
    margin-bottom: 1rem;

    .item-row {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem;
      border: 1px solid var(--color-gainsboro-100);
      border-radius: 8px;
      background-color: #ffffff;

      .item-checkbox {
        margin-top: 0.25rem;

        input[type="checkbox"] {
          width: 16px;
          height: 16px;
          accent-color: #0d6efd;
        }
      }

      .item-image {
        img {
          width: 120px;
          height: 120px;
          object-fit: cover;
          border-radius: 6px;
          background-color: #f8f9fa;

          &.img-error {
            opacity: 0.7;
            filter: grayscale(20%);
          }
        }
      }

      .item-info {
        flex: 1;

        .item-name {
          font-weight: 600;
          color: #212529;
          margin-bottom: 0.5rem;
        }

        .item-price {
          margin-bottom: 0.5rem;

          .current-price {
            color: #dc3545;
            font-weight: 600;
            margin-right: 0.5rem;
          }

          .old-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 0.9rem;
          }
        }

        .item-option {
          color: #6c757d;
          font-size: 0.9rem;
          margin-bottom: 0.5rem;
        }

        .item-quantity-label {
          color: #6c757d;
          font-size: 0.9rem;
        }
      }

      .item-quantity {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .quantity-btn {
          width: 32px;
          height: 32px;
          border: 1px solid #dee2e6;
          background-color: #ffffff;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-weight: 600;

          &:hover {
            background-color: #f8f9fa;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &.minus {
            color: #dc3545;
          }

          &.plus {
            color: #198754;
          }
        }

        .quantity-value {
          min-width: 30px;
          text-align: center;
          font-weight: 600;
        }
      }

      .item-remove {
        .remove-btn {
          width: 24px;
          height: 24px;
          border: none;
          background-color: transparent;
          color: #dc3545;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background-color: #f8f9fa;
          }
        }
      }
    }

    .item-note {
      margin-top: 0.75rem;
      padding: 0.75rem;
      background-color: white;
      border-radius: 6px;
      color: #6c757d;
      font-size: 0.9rem;
    }
  }
}

// ===== SELECTION SECTIONS =====
.selection-section {
  margin-bottom: 2rem;

  .selection-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
  }

  .selection-options {
    &.horizontal {
      display: flex;
      gap: 1rem;

      .option-item {
        flex: 1;
      }
    }

    .option-item {
      margin-bottom: 0.75rem;

      input[type="radio"] {
        display: none;
      }

      .option-label {
        display: block;
        padding: 1rem;
        border: 1px solid var(--color-gainsboro-100);
        border-radius: 8px;
        background-color: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 100%;

        &:hover {
          border-color: #0d6efd;
          background-color: #f8f9fa;
        }

        .option-name {
          font-weight: 600;
          color: #212529;
          margin-bottom: 0.5rem;
        }

        .option-phone {
          color: #6c757d;
          font-size: 0.9rem;
          margin-bottom: 0.25rem;
        }

        .option-address {
          color: #6c757d;
          font-size: 0.9rem;
        }
      }

      input[type="radio"]:checked + .option-label {
        border-color: #0d6efd;
        background-color: #e7f3ff;
      }
    }
  }

  // Customer address selection specific styles
  .no-addresses-message {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;

    p {
      margin-bottom: 0.5rem;
    }

    a {
      color: #0d6efd;
      text-decoration: none;
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .selection-options.vertical {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .customer-option-item {
      position: relative;

      input[type="radio"] {
        display: none;
      }

      .customer-option-label {
        display: block;
        padding: 1.25rem;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        background-color: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          border-color: #0d6efd;
          background-color: #f8f9fa;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
        }

        .default-badge {
          position: absolute;
          top: -8px;
          right: 12px;
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 0.75rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 4px;
          box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);

          .badge-icon {
            width: 14px;
            height: 14px;
          }
        }

        .customer-info {
          .customer-name,
          .customer-phone,
          .customer-address {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .info-icon {
              width: 16px;
              height: 16px;
              color: #6c757d;
              flex-shrink: 0;
            }
          }

          .customer-name {
            font-weight: 600;
            color: #212529;
            font-size: 1rem;
          }

          .customer-phone {
            color: #0d6efd;
            font-weight: 500;
            font-size: 0.9rem;
          }

          .customer-address {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 0;
          }
        }
      }

      input[type="radio"]:checked + .customer-option-label {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);

        .customer-info {
          .customer-name {
            color: #0d6efd;
          }
        }
      }
    }
  }
}

// ===== RIGHT SECTION =====
.right-section {
  flex: 1;
  max-width: 400px;

  @media (max-width: 992px) {
    max-width: none;
  }

  .right-panel {
    background-color: #ffffff;
    border: 1px solid var(--color-gainsboro-100);
    border-radius: 12px;
    padding: 1.5rem;

    .section-divider {
      border: none;
      border-top: 1px solid #dee2e6;
      margin: 1.5rem 0;
    }
  }
}

// ===== COUPON SECTION =====
.coupon-section {
  margin-bottom: 1.5rem;

  .coupon-input {
    display: flex;
    gap: 0.5rem;

    .coupon-field {
      flex: 1;
      padding: 0.75rem;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }

      &::placeholder {
        color: #6c757d;
      }
    }

    .coupon-btn {
      padding: 0.75rem 1.5rem;
      background-color: var(--color-steelblue-300);
      color: var(--color-white);
      border: none;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;

      &:hover {
        background-color: #0b5ed7;
      }
    }
  }
}

// ===== SUMMARY SECTION =====
.summary-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;

  .summary-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 1rem;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;

    &.total {
      font-weight: 600;
      font-size: 1rem;
      padding-top: 0.75rem;
      border-top: 1px solid #dee2e6;
      margin-top: 1rem;

      .summary-value {
        color: #0d6efd;
        font-size: 1.1rem;
      }
    }

    .summary-label {
      color: #333;
    }

    .summary-value {
      color: #333;

      &.discount {
        color: #dc3545;
        font-weight: 600;
      }
    }
  }
}

// ===== PAYMENT SECTION =====
.payment-section {
  margin-bottom: 1.5rem;

  .payment-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
  }

  .payment-options {
    &.horizontal {
      display: flex;
      gap: 0.75rem;
      flex-wrap: wrap;

      .payment-option {
        flex: 1;
        min-width: 0;
      }
    }

    .payment-option {
      margin-bottom: 0.75rem;

      input[type="radio"] {
        display: none;
      }

      .payment-label {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 0.5rem;
        border: 1px solid var(--color-gainsboro-100);
        border-radius: 8px;
        background-color: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        height: 100%;

        &:hover {
          border-color: #0d6efd;
          background-color: #f8f9fa;
        }

        .payment-icon {
          font-size: 1.2rem;
        }

        .payment-name {
          font-weight: 600;
          color: #212529;
          font-size: 0.85rem;
        }
      }

      input[type="radio"]:checked + .payment-label {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
      }
    }
  }
}

// ===== BANK SECTION =====
.bank-section {
  margin-bottom: 1.5rem;

  .bank-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
  }

  .bank-options {
    &.wrap {
      display: flex;
      gap: 0.75rem;
      flex-wrap: wrap;

      .bank-option {
        flex: 1;
        min-width: 120px;
        max-width: 150px;
      }
    }

    .bank-option {
      margin-bottom: 0.75rem;

      input[type="radio"] {
        display: none;
      }

      .bank-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 0.5rem;
        border: 1px solid var(--color-gainsboro-100);
        border-radius: 8px;
        background-color: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        height: 100%;

        &:hover {
          border-color: #0d6efd;
          background-color: #f8f9fa;
        }

        .bank-logo {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }

        .bank-name {
          font-weight: 600;
          color: #212529;
          font-size: 0.85rem;
        }
      }

      input[type="radio"]:checked + .bank-label {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
      }
    }
  }
}

// ===== ORDER BUTTON =====
.order-btn {
  width: 100%;
  padding: 1rem;
  background-color: var(--color-steelblue-300);
  color: var(--color-white);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #0b5ed7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .pay-container {
    padding: 1rem 0.5rem;
  }

  .pay-content {
    gap: 1rem;
  }

  .cart-section {
    padding: 1rem;
  }

  .cart-header .cart-title {
    font-size: 1.25rem;
  }

  .cart-items .cart-item .item-row {
    flex-direction: column;
    gap: 0.75rem;

    .item-checkbox {
      align-self: flex-start;
    }

    .item-image img {
      width: 100%;
      max-width: 200px;
      height: 150px;
    }

    .item-quantity {
      align-self: flex-start;
    }

    .item-remove {
      align-self: flex-end;
    }
  }

  .summary-section {
    .summary-card {
      padding: 1rem;
    }

    .discount-input {
      flex-direction: column;

      .apply-btn {
        align-self: stretch;
      }
    }
  }
}

@media (max-width: 576px) {
  .pay-container {
    padding: 0.75rem 0.25rem;
  }

  .cart-items .cart-item .item-row {
    padding: 0.75rem;

    .item-image img {
      width: 100%;
      height: 120px;
    }

    .item-info {
      .item-name {
        font-size: 0.9rem;
      }

      .item-price .current-price {
        font-size: 0.9rem;
      }
    }

    .item-quantity .quantity-btn {
      width: 28px;
      height: 28px;
    }
  }

  .summary-section .summary-card {
    .order-summary {
      .summary-row {
        font-size: 0.85rem;

        &.total-row {
          font-size: 0.9rem;

          .total-amount {
            font-size: 1rem;
          }
        }
      }
    }

    .payment-btn {
      padding: 0.75rem;
      font-size: 0.9rem;
    }
  }
}

// ===== ORDER SUMMARY =====
.order-summary-card {
  top: 2rem;

  .discount-section {
    .input-group {
      .form-control {
        border-color: var(--color-gainsboro-100, #e0e0e0);

        &:focus {
          border-color: var(--color-steelblue-100, #4a90a4);
          box-shadow: 0 0 0 0.2rem rgba(74, 144, 164, 0.25);
        }
      }

      .btn-primary {
        background-color: var(--color-steelblue-100, #4a90a4);
        border-color: var(--color-steelblue-100, #4a90a4);

        &:hover {
          background-color: var(--color-steelblue-200, #3a7a94);
          border-color: var(--color-steelblue-200, #3a7a94);
        }
      }
    }
  }

  .order-summary {
    .summary-title {
      color: var(--color-steelblue-100, #4a90a4);
      font-weight: 600;
      font-size: var(--font-size-16, 16px);
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.75rem;
      font-size: var(--font-size-14, 14px);

      &.total-row {
        font-weight: 600;
        font-size: var(--font-size-16, 16px);
        color: var(--color-darkgray-100, #333);

        .total-amount {
          color: var(--color-steelblue-100, #4a90a4);
          font-size: var(--font-size-18, 18px);
        }
      }
    }

    hr {
      border-color: var(--color-gainsboro-100, #e0e0e0);
      margin: 1rem 0;
    }
  }

  .payment-btn {
    background-color: var(--color-steelblue-100, #4a90a4);
    border-color: var(--color-steelblue-100, #4a90a4);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--br-5, 5px);
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--color-steelblue-200, #3a7a94);
      border-color: var(--color-steelblue-200, #3a7a94);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 992px) {
  .order-summary-card {
    position: static !important;
    top: auto !important;
  }
}

@media (max-width: 768px) {
  .cart-item {
    .row {
      --bs-gutter-x: 0.5rem;
    }

    .product-info {
      .product-name {
        font-size: var(--font-size-13, 13px);
      }

      .price-info {
        .current-price {
          font-size: var(--font-size-13, 13px);
        }
      }
    }

    .quantity-controls {
      .quantity-btn {
        width: 28px;
        height: 28px;
      }
    }
  }

  .order-summary {
    .summary-row {
      font-size: var(--font-size-13, 13px);

      &.total-row {
        font-size: var(--font-size-14, 14px);

        .total-amount {
          font-size: var(--font-size-16, 16px);
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .pay-container {
    padding: 0.75rem 0.25rem;
  }

  .card {
    .card-header {
      padding: 0.75rem 1rem;
    }

    .card-body {
      padding: 0.75rem;
    }
  }

  .cart-item {
    padding: 0.75rem;
  }

  .payment-methods {
    .form-check-label {
      padding: 0.5rem;

      .payment-name {
        font-size: var(--font-size-13, 13px);
      }
    }
  }
}

// ===== LOADING STATE =====
.spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--color-steelblue-100, #4a90a4);
}

// ===== RESPONSIVE DESIGN =====
@media screen and (max-width: 750px) {
  .pay-container {
  }

  .pay-content {
    gap: 1rem;
  }

  .left-section .section-header .section-title {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 450px) {
  .pay-container {
    box-sizing: border-box;
  }

  .pay-content {
    gap: 0.75rem;
  }

  .cart-items .cart-item .item-container .item-header {
    padding: 0.75rem;
  }
  .selection-section {
    padding: 0.75rem;
  }
  .right-section {
    padding: 0.75rem;
  }
  .pay-section-header {
    padding: 0.75rem;
  }
  .right-section .right-panel {
    padding: 1rem;
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .payment-btn,
  .form-check-label {
    transition: none;
  }
}
