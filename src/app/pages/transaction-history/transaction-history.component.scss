.order-history-container {
  padding: 20px;
  padding-top: 80px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 2px solid #e9ecef;

  .page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;

    .title-icon {
      width: 24px;
      height: 24px;
      color: #0d6efd;
    }
  }

  .refresh-button {
    color: #666;
    transition: all 0.3s ease;

    &:hover {
      color: #0d6efd;
      transform: scale(1.1);
    }

    .spinning {
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #666;

  p {
    margin-top: 1rem;
    font-size: 1rem;
  }
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-item {
  background: white;
  border-radius: 16px;
  border: 2px solid #e9ecef;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
  }

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;

    .order-id {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 600;
      color: #333;

      .order-icon {
        width: 18px;
        height: 18px;
        color: #0d6efd;
      }

      .order-number {
        font-family: monospace;
        font-size: 0.95rem;
        color: #0d6efd;
      }
    }

    .status-badge {
      display: inline-block;
      font-size: 14px;
      font-weight: 600;
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 16px;
      text-align: center;
      min-width: 80px;
    }
  }

  .order-details {
    margin-bottom: 1.25rem;

    .detail-row {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
      font-size: 0.9rem;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-icon {
        width: 16px;
        height: 16px;
        color: #6c757d;
        flex-shrink: 0;
      }

      .detail-label {
        color: #6c757d;
        font-weight: 500;
        min-width: 80px;
      }

      .detail-value {
        color: #333;
        font-weight: 500;
        flex: 1;

        &.total-amount {
          color: #0d6efd;
          font-weight: 600;
          font-size: 1rem;
        }
      }
    }
  }

  .order-products-preview {
    margin-bottom: 1.25rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    .products-summary {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;

      .products-icon {
        width: 16px;
        height: 16px;
        color: #6c757d;
      }

      .products-count {
        font-weight: 600;
        color: #0d6efd;
      }

      .products-names {
        color: #666;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .order-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;

    .detail-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      border-radius: 8px;
      padding: 0.5rem 1rem;

      .button-icon {
        width: 16px;
        height: 16px;
      }

      &:hover {
        background-color: #0d6efd;
        color: white;
      }
    }
  }
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;

  .load-more-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;

    .button-spinner {
      margin-right: 0.5rem;
    }

    .button-icon {
      width: 18px;
      height: 18px;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: white;
  border-radius: 16px;
  border: 2px solid #e9ecef;

  .empty-icon {
    width: 64px;
    height: 64px;
    color: #6c757d;
    margin-bottom: 1.5rem;
    opacity: 0.7;
  }

  h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
  }

  p {
    margin: 0 0 2rem 0;
    color: #666;
    line-height: 1.5;
    max-width: 400px;
  }

  .shop-now-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;

    .button-icon {
      width: 18px;
      height: 18px;
    }
  }
}

// Status button styles
.pending-button { background-color: #ffc107 !important; color: white; }
.confirmed-button { background-color: #17a2b8 !important; color: white; }
.preparing-button { background-color: #fd7e14 !important; color: white; }
.shipping-button { background-color: #007bff !important; color: white; }
.delivered-button { background-color: #28a745 !important; color: white; }
.cancelled-button { background-color: #dc3545 !important; color: white; }
.returned-button { background-color: #6c757d !important; color: white; }
.payment-pending-button { background-color: #e83e8c !important; color: white; }
.unknown-button { background-color: #6c757d !important; color: white; }

// Responsive design
@media (max-width: 768px) {
  .order-history-container {
    padding: 1rem;
  }

  .page-header {
    padding: 1rem;
    margin-bottom: 1.5rem;

    .page-title {
      font-size: 1.25rem;
    }
  }

  .order-item {
    padding: 1rem;

    .order-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .order-details .detail-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;

      .detail-label {
        min-width: auto;
      }
    }

    .order-actions {
      justify-content: center;

      .detail-button {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .empty-state {
    padding: 3rem 1.5rem;

    .empty-icon {
      width: 48px;
      height: 48px;
    }

    h3 {
      font-size: 1.1rem;
    }
  }
}

// Global dialog styles
:host ::ng-deep .order-detail-dialog-container {
  .mat-mdc-dialog-container {
    padding: 0;
    border-radius: 16px;
    overflow: hidden;
  }

  .mat-mdc-dialog-surface {
    border-radius: 16px;
  }
}
