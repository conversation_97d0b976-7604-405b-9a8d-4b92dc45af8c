# Hướng Dẫn Setup Giỏ Hàng và Thanh Toán

## 1. Files Cần Import

### 1.1 Thêm vào CoreModule
```typescript
// src/app/core/core.module.ts
import { CartService } from './services/cart.service';
import { OrderService } from './services/order.service';

@NgModule({
  providers: [
    // ... existing providers
    CartService,
    OrderService,
  ]
})
```

### 1.2 Thêm Route cho Order Success
```typescript
// src/app/app.routes.ts
import { OrderSuccessComponent } from './pages/order-success/order-success.component';

export const routes: Routes = [
  // ... existing routes
  { path: 'order-success', component: OrderSuccessComponent },
  { path: 'pay', component: PayComponent },
];
```

## 2. Environment Configuration

```typescript
// src/environments/environment.ts
export const environment = {
  // ... existing config
  apiUrl: 'https://api.langnuoibienvandon.com/v1',
  brandId: '623a9950be781e0ba814f22a', // ID cố định cho brand
};
```

## 3. API Service Integration

Đảm bảo ApiService có các methods:
```typescript
// src/app/core/services/api.service.ts
get(endpoint: string, params?: any): Observable<any>
post(endpoint: string, body: any, params?: any): Observable<any>
```

## 4. Auth Service Integration

Đảm bảo AuthService có:
```typescript
// src/app/core/services/auth.service.ts
isAuthenticated(): boolean
getCurrentUser(): UserModel | null
```

## 5. Sử Dụng trong Components

### 5.1 Product Detail Dialog
```typescript
// Đã được cập nhật với logic:
onAddToCart() // Thêm vào giỏ hàng
onBuyNow()    // Mua ngay
```

### 5.2 Pay Component  
```typescript
// Đã được cập nhật với:
processPayment() // Xử lý thanh toán
applyDiscountCode() // Áp dụng mã giảm giá
```

## 6. Testing

### 6.1 Test Add to Cart
1. Mở product detail dialog
2. Chọn thuộc tính (nếu có)
3. Nhấn "Thêm vào giỏ"
4. Kiểm tra localStorage có dữ liệu
5. Kiểm tra toast notification

### 6.2 Test Buy Now
1. Mở product detail dialog
2. Nhấn "Mua ngay"
3. Kiểm tra chuyển đến trang /pay
4. Kiểm tra sản phẩm hiển thị trong giỏ

### 6.3 Test Cash Payment
1. Vào trang /pay
2. Chọn "Thanh toán tiền mặt"
3. Nhấn "Thanh toán"
4. Kiểm tra API call được gọi
5. Kiểm tra chuyển đến order-success

## 7. API Endpoints Cần Hoạt Động

```
GET  /user/api/chi-tiet-san-pham/{id}.html?output=json
POST /user/api/tai-thong-tin-gio-hang.html?output=json
POST /user/api/create-order-product?output=json
POST /user/api/check-coupon?output=json
GET  /user/api/services-of-brand/{brandId}/show-room?output=json
```

## 8. Troubleshooting

### 8.1 Lỗi "Service not found"
- Kiểm tra đã import services vào CoreModule
- Kiểm tra providedIn: 'root' trong services

### 8.2 Lỗi API calls
- Kiểm tra environment.apiUrl
- Kiểm tra authentication headers
- Kiểm tra CORS settings

### 8.3 LocalStorage issues
- Kiểm tra browser support
- Kiểm tra storage quota
- Clear cache nếu cần

### 8.4 Navigation issues
- Kiểm tra routes đã được define
- Kiểm tra component imports
- Kiểm tra lazy loading

## 9. Production Checklist

- [ ] Test tất cả API endpoints
- [ ] Test responsive design
- [ ] Test error scenarios
- [ ] Add loading states
- [ ] Add analytics tracking
- [ ] Configure error monitoring
- [ ] Test performance
- [ ] Security review
- [ ] Accessibility check
- [ ] Browser compatibility test

## 10. Monitoring và Analytics

### 10.1 Events cần track
```typescript
// Add to cart
analytics.track('add_to_cart', {
  product_id: productId,
  quantity: quantity,
  value: price
});

// Purchase
analytics.track('purchase', {
  order_id: orderId,
  value: totalAmount,
  currency: 'VND'
});
```

### 10.2 Error monitoring
```typescript
// Sentry integration
Sentry.captureException(error, {
  tags: {
    section: 'cart',
    action: 'add_to_cart'
  }
});
```

## 11. Performance Optimization

### 11.1 Lazy loading
- Lazy load OrderSuccessComponent
- Lazy load PayComponent nếu cần

### 11.2 Caching
- Cache product details
- Cache branch stores
- Debounce quantity updates

### 11.3 Bundle optimization
- Tree shake unused code
- Optimize imports
- Use OnPush change detection

## 12. Security Considerations

### 12.1 Input validation
- Validate all user inputs
- Sanitize HTML content
- Check price manipulation

### 12.2 API security
- Use HTTPS only
- Validate tokens
- Rate limiting
- CSRF protection

## 13. Backup và Recovery

### 13.1 LocalStorage backup
- Implement sync với server
- Handle storage failures
- Provide manual backup

### 13.2 Order recovery
- Save draft orders
- Resume interrupted payments
- Handle network failures

Với setup này, bạn sẽ có một hệ thống giỏ hàng và thanh toán hoàn chỉnh, tương tự như logic mobile app nhưng được tối ưu cho web.
