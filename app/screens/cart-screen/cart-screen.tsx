import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  FlatList,
  Image,

  Text,
  TouchableOpacity,
  View
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

import styles from './styles'

import { <PERSON><PERSON><PERSON>, Header } from 'react-native-elements'
import Modal from 'react-native-modal'
import { numberFormat } from '@app/utils/number'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { editProduct } from '@app/services'
import { icEmptyCart, imageDiscount } from '../../assets/images'
import { LazyImage, ButtonBack, TButton, TTextInput, Loading, ConfirmDialog } from '@app/components'
import { InputCount } from '@app/services/InputCount'
import Icon from 'react-native-vector-icons/Ionicons'
import { useAbortableEffect } from '@app/use-hooks'
import { useAuth } from '@app/use-hooks/use-auth'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { color } from '@app/theme'
import { Modalize } from 'react-native-modalize'
import { ModalContext } from '@app/components/modal-success'
import LinearGradient from 'react-native-linear-gradient'
import { SCREENS } from '@app/navigation'

const showPrice = (item) => {
  if (item.classifyActive && Object.keys(item.classifyActive).length) {
    const regex = new RegExp(',', 'g')
    const p = item.classifyActive.price.replace(regex, '')
    // giá con
    return <View>
      <Text style={styles.sPText}>{numberFormat(parseInt(p))} <Text style={[styles.sPText, { textDecorationLine: 'underline' }]}>đ</Text></Text>
    </View>
  } else {
    // fixed price
    return <Text style={styles.sPText}>{numberFormat(item.info.price)} <Text style={[styles.sPText, { textDecorationLine: 'underline' }]}>đ</Text></Text>
  }
}

const RenderItemProduct = observer((props: any) => {
  const { item, index, indexShop } = props
  const { t } : any = useTranslation()
  const { productStore } = useStores()
  const { updateShoppingCount } = useAuth() // should be signUp
  const [reloadData, setReloadData] = useState(false)

  const changeProductSelect = async (indexProduct, indexShop) => {
    // setSubmitting(true)
    __DEV__ && console.log('indexProduct', indexProduct)
    __DEV__ && console.log('indexShop', indexShop)
    productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[indexProduct].select = !productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[indexProduct].select
    if (!productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[indexProduct].select) {
      productStore.shopProducts[indexShop].select = false
    } else {
      let allChildIsSelect = true
      productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products.forEach((p, iP) => {
        if (!p.select) allChildIsSelect = false
      })
      productStore.shopProducts[indexShop].select = allChildIsSelect
    }
    productStore.shopProducts[indexShop].haveItem = productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products.filter(product => product.select === true).length > 0 // haveItem biến lưu shop có sản phẩm được chọn > 0
    productStore.setShopProducts(productStore.shopProducts)
    setReloadData(!reloadData)
    // setReCalculate(!reCalculate) // TODO
    productStore.setReCalculate()
    props.onCheckedChanged()
  }

  const changeProductCount = async (indexProduct, indexShop, newCount) => {
    // setSubmitting(true)
    __DEV__ && console.log('indexProduct', indexProduct)
    __DEV__ && console.log('indexShop', indexShop)
    __DEV__ && console.log('newCount', newCount)
    productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[indexProduct].count = Number(newCount)
    await editProduct(productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[indexProduct].productId, Number(newCount), updateShoppingCount)
    productStore.setShopProducts(productStore.shopProducts)
    // setReloadData(!reloadData)
    // setReCalculate(!reCalculate)
    productStore.setReCalculate() // TODO
  }

  const onChangeTextNote = async (indexProduct, indexShop, newNote) => {
    productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[indexProduct].noteProduct = newNote
    productStore.setShopProducts(productStore.shopProducts)
  }

  return (
    <View style={{ backgroundColor: 'white' }}>
      <View style={styles.rSp}>
        <CheckBox
          iconType='ionicon'
          checkedIcon='checkbox-outline'
          uncheckedIcon='square-outline'
          checkedColor={color.primary}
          checked={item.select}
          onPress={() => changeProductSelect(index, indexShop)}
          containerStyle={{ padding: 0, margin: 0, marginLeft: 0, alignSelf: 'center' }}
          size={20}
        />
        <View style={styles.rSpView}>
          <LazyImage source={{ uri: item.info.thumbail }} style={styles.rSpViewImage}/>
        </View>
        <View style={styles.rSpView1}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text numberOfLines={2} style={styles.rSpView1Text}>{item.info.name}</Text>
            {/* <TouchableOpacity style={styles.rSpView2Top} */}
            {/*  onPress={() => deleteProduct(index, indexShop)}> */}
            {/*  <Icon name={'close-outline'} size={20} color={'#333'}/> */}
            {/* </TouchableOpacity> */}
            <TouchableOpacity style={styles.rSpView2Top}
              onPress={() => {
                // showAlert()
                // props.showAlert(index, indexShop)
                productStore.setSelectProductRemove(index, indexShop)
                props.onConfirmDeleteProduct()
                // setIndex(index)
                // setIndexShop(indexShop)
              }}>
              <Icon name={'close-outline'} size={20} color={'#333'}/>
            </TouchableOpacity>
          </View>
          <View>
            {showPrice(item)}
            {/* {item.classifies.map((e, index) => { */}
            {/*  if (e.index == 0) { */}
            {/*    return <Text key={index} */}
            {/*      style={styles.rSpView1Text1}>{item.classifyActive.name}: <Text style={{ fontSize: 13, color: '#333' }}>{item.classifyActive.value}</Text></Text> */}
            {/*  } */}
            {/*  return null */}
            {/* })} */}
            {item.classifyActive?.name ? <Text key={index} style={styles.rSpView1Text1}>{item.classifyActive.name}: <Text style={{ fontSize: 13, color: '#333' }}>{item.classifyActive.value}</Text></Text> : null}
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={styles.rSpView1Text2}>{t('COUNT')}</Text>
            <InputCount style={styles.rSpView1Input}
              onChangeText={(count) => {
                // console.log('count', count)
                changeProductCount(index, indexShop, count)
              }}
              value={item.count}/>
          </View>
        </View>
      </View>
      <View style={{ marginHorizontal: 16, marginBottom: 10 }}>
        <TTextInput
          typeRadius={'rounded'}
          autoCapitalize={'none'}
          placeholder={t('Ghi chú sản phẩm, màu sắc, kích cỡ...')}
          placeholderTextColor={'#a0a0a0'}
          underlineColorAndroid="transparent"
          onChangeText={(e) => onChangeTextNote(index, indexShop, e)}
          defaultValue={item.noteProduct}
        />
      </View>
    </View>
  )
})

const RenderShopView = observer(({ item, indexShop } : any) => {
  const { t } : any = useTranslation()
  const { productStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const [isShowConfirmDeleteProd, setIsShowConfirmDeleteProd] = useState(false)
  const { updateShoppingCount } = useAuth() // should be signUp
  const [reloadData, setReloadData] = useState(false)

  const onCheckedChanged = () => {
    __DEV__ && console.log('Emit from product list')
    setReloadData(!reloadData)
  }

  // login check all of shop
  const changeShopSelect = async (indexShop) => {
    // setSubmitting(true)
    productStore.shopProducts[indexShop].select = !productStore.shopProducts[indexShop].select // toggle true false select all of shop
    productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products.forEach((p, iP) => {
      productStore.shopProducts[indexShop][productStore.shopProducts[indexShop].info._id].products[iP].select = productStore.shopProducts[indexShop].select
    })
    productStore.shopProducts[indexShop].haveItem = productStore.shopProducts[indexShop].select // haveItem biến lưu shop có sản phẩm được chọn > 0
    productStore.setShopProducts(productStore.shopProducts)
    setReloadData(!reloadData)
    // setReCalculate(!reCalculate)
    productStore.setReCalculate()
  }

  const deleteProduct = async () => {
    // productStore.setGeneralCoupon('')
    // setSubmitting(true)
    await productStore.removeCartItem(updateShoppingCount)
    productStore.setReCalculate()
    // setReloadData(!reloadData)
    // setReCalculate(!reCalculate)
  }

  const onConfirmDeleteProduct = () => {
    setIsShowConfirmDeleteProd(true)
  }

  __DEV__ && console.log('Item Shop Render ****', item)
  __DEV__ && console.log('Item Shop indexShop ****', indexShop)
  return <View style={styles.rsView}>
    <View style={styles.viewStoreAddress}>
      <View style={styles.rsView1}>
        <CheckBox
          iconType='ionicon'
          checkedIcon='checkbox-outline'
          uncheckedIcon='square-outline'
          checkedColor='#CB1016'
          checked={item.select}
          onPress={() => changeShopSelect(indexShop)}
          containerStyle={{ padding: 0, margin: 0, marginLeft: 0 }}
          size={20}
        />
        <TouchableOpacity
          // onPress={() => this.props.open('ChiTietCuaHang', {shopId: item.info._id, name: item.info.name, })}
          style={{ flex: 1 }}
        >
          <Text style={styles.rsView1Text}>{item.info.name}</Text>
        </TouchableOpacity>

        {/* <TouchableOpacity */}
        {/*  // onPress={() => taiNganHangChuShop(item.info.userId)} */}
        {/*  style={styles.rsView1Top}> */}
        {/*  <Text style={styles.rsView1TopText}>{t('BANKTRANSFER')}</Text> */}
        {/* </TouchableOpacity> */}
      </View>
      <View style={styles.rsView2}>
        {/* <Image style={styles.icLocation} source={icLocation}/> */}
        <Icon name={'location-outline'} size={20} color={color.primary}/>
        <Text style={styles.rsView2Text}>{item.info.address}</Text>
      </View>
    </View>
    <FlatList
      style={{ backgroundColor: color.primaryBackground }}
      data={item[item.info._id].products}
      extraData={item[item.info._id].products}
      keyExtractor={(item, index) => item._id + index.toString()}
      renderItem={({ item, index }) => <RenderItemProduct onConfirmDeleteProduct={onConfirmDeleteProduct} onCheckedChanged={onCheckedChanged} item={item} index={index} indexShop={indexShop} />}/>
    <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} isVisible={isShowConfirmDeleteProd} message={t('CHACCHANMUONXOABOSP')} title={'Nhắc nhở'}
      onConfirm={() => {
        setIsShowConfirmDeleteProd(false)
        setTimeout(() => { deleteProduct() }, 500)
      }}
      onClosed={() => setIsShowConfirmDeleteProd(false)}
    />
  </View>
})

const RenderPriceTotalView = observer(({ count, loading } : any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { productStore } = useStores()

  return (
    <View style={styles.sAvView3}>
      {/* <View style={styles.sAvView2}> */}
      {/*  <View style={styles.viewTtinput}> */}
      {/*    <TTextInput */}
      {/*      keyboardType="phone-pad" */}
      {/*      maxLength={16} */}
      {/*      autoCapitalize={'none'} */}
      {/*      placeholderStyle={{ textAlign: 'center' }} */}
      {/*      iconRightClick={() => { }} */}
      {/*      value={productStore.coupon} */}
      {/*      placeholder={t('NHAPMAGIAMGIA')} */}
      {/*      underlineColorAndroid="transparent" */}
      {/*      // style={styles.sAvView2Text} */}
      {/*      typeRadius={'rounded'} */}
      {/*      onChangeText={setCoupon} */}
      {/*    /> */}
      {/*  </View> */}
      {/*  <TButton typeRadius={'rounded'} buttonStyle={styles.btnSubmitCT1} */}
      {/*    title={t('APDUNG')} onPress={onCalculate} /> */}
      {/* </View> */}

      <View style={{ justifyContent: 'center' }}>
        <View style={styles.sAvView31}>
          <Text style={styles.sAvView31Text}>{t('COUNT')}: </Text>
          <Text style={styles.sAvView31Text1}>{count}</Text>
        </View>
        {/* {productStore.valueCoupon ? <View style={styles.sAvView4}> */}
        {/*  /!* //TODO cần phải thêm mã giảm giá được áp dụng trên đơn hàng bao nhiêu tiền *!/ */}
        {/*  <Text */}
        {/*    style={{ */}
        {/*      fontSize: 13, */}
        {/*      flex: 1, */}
        {/*      color: (productStore.totalPrice >= 0) ? '#333' : '#ccc', */}
        {/*    }}>{t('MAGIAMGIA/DONHANG')} 50.000 đ)</Text> */}
        {/*  <Text */}
        {/*    style={{ */}
        {/*      fontSize: 13, */}
        {/*      color: (productStore.totalPrice >= 0) ? '#333' : '#ccc', */}
        {/*      marginTop: 5, */}
        {/*    }}>-{productStore.valueCoupon ? productStore.valueCoupon + '%' : numberFormat(productStore.totalPrice) + ' đ'}</Text> */}
        {/* </View> : null} */}
        <View style={styles.sAvView5}>
          <Text style={styles.sAvView5Text}>{t('TOTAL_MONEY')}: </Text>
          <Text style={styles.sAvView5Text1}>{numberFormat(productStore.generalPrice ? productStore.generalPrice : productStore.totalPrice)} <Text style={[styles.sAvView5Text1, { textDecorationLine: 'underline' }]}>đ</Text></Text>
        </View>
      </View>
      {count > 0 ? <TButton typeRadius={'rounded'} disabled={loading} loading={loading} buttonStyle={styles.btnSubmitCT}
        title={t('THANHTOAN')}
        titleStyle={{ fontSize: 16, fontWeight: '500' }}
        onPress={() => {
          navigate(SCREENS.reviewBookingProductScreen, { bookingData: productStore.shopProducts, tongTien: productStore.totalPrice })
        }} /> : null}
    </View>
  )
})

export const CartScreen = observer(function CartScreen(props: any) {
  const { t } : any = useTranslation()
  const { productStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const [reloadData, setReloadData] = useState(false)
  const [modalThongTinChuyenKhoan, setModalThongTinChuyenKhoan] = useState(false)
  const [banks] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const { updateShoppingCount } = useAuth() // should be signUp
  const modalInputDiscount = useRef<Modalize>(null)
  const [discountValue, setDiscountValue] = useState(0)
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const [isSubmitting, setSubmitting] = useState(false)
  const route: any = useRoute()

  useAbortableEffect(() => {
    // removeAllInCart('') // Khi giỏ hàng lỗi gọi remove all
    productStore.clearFieldsCoupon()
    loadData().then(r => {
      onCalculate()
    })
  }, [])

  useEffect(() => {
    if (productStore.shopProducts.length > 0) {
      __DEV__ && console.warn('**** productStore.reCalculate')
      setTimeout(() => { onCalculate().then(r => {}) }, 200)
    } else {
      resetCoupon()
    }
  }, [productStore.reCalculate])

  useEffect(() => {
    if (route.params?.updateShoppingCount === true) {
      __DEV__ && console.warn('**** route.params onCalculate')
      loadData().then(r => {
        onCalculate()
      })
      // productStore.setReloadData(false)
    }
  }, [route.params])

  // useEffect(() => {
  //   __DEV__ && console.log('**** isBuyNow')
  //   if (isBuyNow) {
  //     __DEV__ && console.log('**** isBuyNow  *** 2')
  //     onCalculate()
  //   }
  // }, [isBuyNow])

  const onGoBack = () => {
    goBack()
    setTimeout(() => {
      productStore.setGeneralCoupon('')
      productStore.setTotalDisCount('')
    }, 100)
  }

  const loadData = async () => {
    await productStore.fetchCartData(updateShoppingCount)
    // setReloadData(!reloadData)
    // setReCalculate(!reCalculate)
    setIsLoading(false)
  }

  const onOpen = () => {
    modalInputDiscount.current?.open()
  }
  const onClose = () => {
    modalInputDiscount.current?.close()
  }

  const itemBanks = ({ item, index }) => {
    return <View style={styles.iView}>
      <View style={styles.iView1}>
        <Text
          style={styles.iView1Text}>{t('TENNGANHANG')}</Text>
        <Text
          style={styles.iView1Text1}>{item.bankName}</Text>
      </View>
      <View style={styles.iView2}>
        <Text
          style={styles.iView1Text}>{t('CHINHANH')}</Text>
        <Text
          style={styles.iView1Text1}>{item.bankBrnach}</Text>
      </View>

      <View style={styles.iView2}>
        <Text
          style={styles.iView1Text}>{t('CHUTAIKHOAN')}</Text>
        <Text
          style={styles.iView1Text1}>{item.fullName}</Text>
      </View>

      <View style={styles.iView2}>
        <Text
          style={styles.iView1Text}>{t('SOTAIKHOAN')}</Text>
        <Text
          style={styles.iView1Text1}>{item.bankNumber}</Text>
      </View>
    </View>
  }

  const onCalculate = async () => {
    setSubmitting(true)
    await productStore.calculateTotalPrice()
    // await updateShoppingCount(productStore.count)
    const rs = await productStore.checkCoupon(productStore.generalCoupon || '', productStore.totalPrice, 0, '')
    if (rs && rs?.data?.error) {
      showCustomError(t('FAIL'), `${rs.data.message}`)
    } else {
      const data = rs?.data?.data
      setDiscountValue(data?.discount || 0)
      productStore.setGeneralCoupon(data?.coupon?.code)
      productStore.setRsCoupon(data?.coupon?.code)
      productStore.setTotalPrice(data.totalPrice)
      productStore.setGeneralPrice(data.totalPrice)
      if (data?.discount) {
        productStore.setTotalDisCount(data.discount)
      } else {
        productStore.setTotalDisCount(0)
        productStore.setRsCoupon('')
      }
      console.log('totalDiscount---', productStore.totalDiscount)
      if (data.rsCoupon.error) {
        productStore.setGeneralCoupon('')
        productStore.clearFieldsCoupon()
        await productStore.calculateTotalPrice()
        showCustomError(t('FAIL'), `${data.rsCoupon.msg}`)
      }
    }
    setSubmitting(false)
  }
  //
  // const onEnterCoupon = async (inputText) => {
  //   if (inputText === '' || !inputText) {
  //     setSubmitting(false)
  //     Alert.alert(t('FAIL'), t('INPUT_COUPON'))
  //   } else if (inputText) {
  //     setIsDialogVisible(false)
  //     bookingStore.setCoupon(inputText)
  //   }

  const resetCoupon = () => {
    productStore.setTotalDisCount('')
    productStore.setGeneralCoupon('')
    productStore.setGeneralPrice('')
    productStore.setRsCoupon('')
  }

  const onClearCoupon = () => {
    resetCoupon()
    setDiscountValue(0)
    productStore.calculateTotalPriceDiscount().then(r => {
      setReloadData(!reloadData)
    })
    setTimeout(() => { onCalculate().then(r => {}) }, 100)
  }

  const EmptyCart = () => {
    return (
      <View style={styles.sAvView1}>
        <Image source={icEmptyCart} style={styles.sAvView1Image} />
        <Text style={styles.sAvView1Text}>{t('CHUACOSANPHAM')}</Text>
        <TouchableOpacity
          style={{ paddingVertical: 20 }}
          onPress={() => onGoBack()}
        >
          <Text style={{ fontSize: 16, fontWeight: '600', color: color.primary }}>{t('Back_to_shopping')}</Text>
        </TouchableOpacity>
      </View>
    )
  }

  const renderFooter = () => {
    return (
      <View style={styles.viewDiscount}>
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          <Text style={{ fontWeight: '500', fontSize: 14, color: '#333' }}>{t('Mã giảm giá maxQ')}</Text>
          {productStore.rsCoupon ? <View style={{ flexDirection: 'row', flex: 1 }}>
            <Text style={styles.discountCode}>{productStore.rsCoupon}</Text>
            <View style={styles.viewDiscountValue}>
              <Text style={styles.discountValue}> - {numberFormat(discountValue)} VNĐ</Text>
            </View>
          </View> : null}
        </View>
        {productStore.rsCoupon ? <TouchableOpacity
          onPress={() => onClearCoupon()}
        >
          <Icon name={'close-outline'} color={color.primary} size={20}/>
        </TouchableOpacity> : <TouchableOpacity
          onPress={onOpen}
          style={{ flexDirection: 'row' }}>
          <Text style={{ fontWeight: '500', fontSize: 14, color: '#979797' }}>Nhập mã giảm giá</Text>
          <Icon name={'chevron-forward-outline'} color={'#979797'} size={17}/>
        </TouchableOpacity>}
      </View>
    )
  }

  const renderHeaderModalInput = () => (
    <View style={styles.modal__header}>
      <TouchableOpacity onPress={() => {
        onClose()
      }}>
        <Text>{t('CANCEL')}</Text>
      </TouchableOpacity>
      {/* <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} /> */}
      <Text style={styles.textTitleHeader}>{t('Mã giảm giá maxQ')}</Text>
      <TouchableOpacity
        onPress={() => {
          onClose()
          setTimeout(() => { onCalculate().then(r => {}) }, 100)
        }}
      >
        <Text style={styles.textDone}>{t('XONG')}</Text>
      </TouchableOpacity>

    </View>
  )

  const renderInputDiscount = () => {
    return (
      <View style={{ paddingBottom: 70, paddingHorizontal: 16 }}>
        <View style={{ alignItems: 'center', paddingVertical: 35 }}>
          <Image source={imageDiscount} style={{ width: 135, height: 120, marginBottom: 10 }}></Image>
          <Text style={styles.description}>Bạn không có mã giảm giá có sẵn nào</Text>
          <Text style={styles.description}>Nhập mã giảm giá của bạn ở bên dưới</Text>
        </View>
        <TTextInput
          typeInput={'code'}
          typeRadius={'rounded'}
          // keyboardType="decimal-pad"
          maxLength={12}
          autoCapitalize={'none'}
          defaultValue={productStore.generalCoupon || ''}
          placeholder={t('NHAPMAGIAMGIA')}
          placeholderStyle={{ textAlign: 'center' }}
          onChangeText={e => productStore.setGeneralCoupon(e)}
          iconRightClick={() => productStore.setGeneralCoupon('')}
          iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
        />
      </View>
    )
  }

  return (
    <SafeAreaView style={styles.sAv}>
      <View style={styles.sAvView}>
        <Header
          // statusBarProps={{ barStyle: 'light-content' }}
          // barStyle="light-content" // or directly
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
          centerComponent={{ text: t('Giỏ hàng'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        {/* <Header position={offset} title={title} onPressButtonLeft={goBack}/> */}
        {!isLoading && productStore.shopProducts.length == 0
          ? <View style={{ flex: 1 }}>{EmptyCart()}</View> : !isLoading ? <View style={{ flex: 1 }}>
            <FlatList
              data={productStore.shopProducts}
              extraData={productStore.shopProducts}
              keyExtractor={(item, index) => item._id + index.toString()}
              renderItem={({ item, index }) => <RenderShopView item={item} indexShop={index}/>}
              ListFooterComponent={renderFooter}
            />
            <RenderPriceTotalView count={productStore.count} loading={isSubmitting} />
          </View> : <Loading/>}
        <Modal style={styles.rMd}
          isVisible={modalThongTinChuyenKhoan}>
          <View style={styles.rMdView}>
            <Text style={styles.rMdViewText}>{t('LSMUAHANG_dsnganhang')}</Text>
            <Text style={styles.rMdViewText1}>{t('LSMUAHANG_chonnganhang')}</Text>
            {banks.length == 0 ? <Text style={styles.rMdViewText2}>{t('LSMUAHANG_nobank')}</Text> : <FlatList data={banks}
              nestedScrollEnabled={true}
              style={{ height: 400 }}
              renderItem={itemBanks}
              keyExtractor={(e, i) => i + 'item-banks'}
            />}
            <View style={styles.rMdView1}>
              <TouchableOpacity onPress={() => setModalThongTinChuyenKhoan(false)} style={{
                padding: 10
              }}>
                <Text style={styles.rMdView1Text}>{t('LSMUAHANG_nobank')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
      <Modalize
        HeaderComponent={renderHeaderModalInput}
        ref={modalInputDiscount}
        adjustToContentHeight
        // snapPoint={405}
        // modalHeight={405}
        // onClosed={() => {}}
        keyboardAvoidingBehavior={'padding'}
      >
        {renderInputDiscount()}
      </Modalize>
    </SafeAreaView>
  )
})
