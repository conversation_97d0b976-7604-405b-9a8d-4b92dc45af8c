import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect } from 'react'
import {

  View,
  Text, TouchableOpacity, FlatList
} from 'react-native'

import styles from './styles'

import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation } from '@react-navigation/native'

import { observer } from 'mobx-react-lite'
import { useStores } from '../../../models/root-store'
import { PlaceHolder, ButtonBack, ConfirmDialog } from '../../../components'
import { SCREENS } from '../../../navigation'
import { useTranslation } from 'react-i18next'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { getSnapshot } from 'mobx-state-tree'
import { UpdateProfileModel } from '@app/models'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'

export const RenderAddress: React.FC = observer((props) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { profileStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [selectedAddressIndex, setSelectedAddressIndex] = useState(-1)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [isShowConfirmDelete, setIsShowConfirmDelete] = useState(false)
  const [index, setIndex] = useState(-1)

  useEffect(() => {
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    if (profileStore.reloadData) {
      loadData().then(r => {})
    }
  }, [profileStore.reloadData])

  const updateDefault = async () => {
    const findDefault = profileStore.addressList.filter(address => address.default === true)
    if (profileStore.addressList.length >= 1 && findDefault.length === 0) {
      const updateItem = { ...profileStore.addressList[0] }
      updateItem.default = true
      profileStore.updateAddressOfList(updateItem)
      setSelectedAddressIndex(0)
      console.log(profileStore.addressList[0])
      const snapShotProfile = getSnapshot(profileStore)
      const body = UpdateProfileModel.create(snapShotProfile)
      const rs = await profileStore.updateProfile(body)
    }
  }

  const loadData = async () => {
    const rs = await profileStore.getProfile()
    if (rs?.data?.error) {
    }
    setIsFetched(false)
    profileStore.setReloadData(false)
    await updateDefault()
  }

  const onSelectedAddress = async (index) => {
    const updateItem = { ...profileStore.addressList[index] }
    updateItem.default = true
    profileStore.updateAddressOfList(updateItem)
    setSelectedAddressIndex(index)
    console.log(profileStore.addressList[index])
    const snapShotProfile = getSnapshot(profileStore)
    const body = UpdateProfileModel.create(snapShotProfile)
    const rs = await profileStore.updateProfile(body)
  }
  /**
   * call Store
   */
  const refreshData = async () => {
    setIsFetched(true)
    setRefreshing(true)
    await profileStore.getProfile()
    await updateDefault()
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    setRefreshing(true)
    refreshData().then(r => {
    })
    setRefreshing(false)
  }
  const onDelete = async (index) => {
    setRefreshing(true)
    profileStore.deleteAddressOfList(index)
    const snapShotProfile = getSnapshot(profileStore)
    const body = UpdateProfileModel.create(snapShotProfile)
    // const body = profileStore
    await profileStore.updateProfile(body)
    // await onRefresh()
    await updateDefault()
    setRefreshing(false)
  }

  const showAlert = () => {
    setIsShowConfirmDelete(true)
  }

  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.boxViewAddress}>
        <View>
          <View style={styles.renderItemListAddress}>
            <Text numberOfLines={1} style={styles.textName}>{item.name}</Text>
            {(selectedAddressIndex > -1 && selectedAddressIndex === index) || item.default
              ? <View style={styles.boxChoose}><Icon name={'checkmark-outline'} size={16} color={'#f3373a'}/><Text
                style={styles.chooseAddress}>{t('DEFAULT-ADDRESS')}</Text></View>
              : <TouchableOpacity onPress={() => {
                onSelectedAddress(index)
              }} style={styles.boxChoose}><Text
                  style={styles.unChooseAddress}>{t('SETDEFAULT-ADDRESS')}</Text></TouchableOpacity>}
          </View>
        </View>
        <View style={styles.boxAddress}>
          <Icon style={styles.icLocation} name={'location-outline'} size={22} color={color.primary}/>
          <Text numberOfLines={2} style={styles.textAddressBranch}>{item.address}</Text>
        </View>
        <View style={styles.renderItemListPhone}>
          <View style={styles.boxViewPhone}>
            <Icon name={'call-outline'} size={21} color={color.primary}/>
            <Text style={styles.textAddressPhone}>{item.phone}</Text>
          </View>
          <View style={styles.boxViewBTN}>
            <TouchableOpacity style={{ marginRight: 12 }} onPress={() => {
              navigate(SCREENS.renderEditAddress, { item: item })
            }}>
              <Icon name={'create'} size={20} color={'#a0a0a0'}/>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              showAlert()
              setIndex(index)
            }}>
              <Icon name={'trash-outline'} size={20} color={'#a0a0a0'}/>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    )
  }

  const renderFooterList = () => {
    return (
      <View style={{ backgroundColor: '#fff', flex: 1, marginVertical: 30 }}>
        <TouchableOpacity style={styles.buttonAdd}
          onPress={() => {
            navigate(SCREENS.renderAddAddress)
          }}
        >
          <Text style={styles.textButton}>{t('ADDADDRESS')}</Text>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t(t('address')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      {isFetched ? <PlaceHolder/>
        : <FlatList
          style={{ backgroundColor: '#fff' }}
          data={profileStore.addressList}
          onRefresh={() => onRefresh()}
          refreshing={refreshing}
          keyExtractor={item => item._id}
          renderItem={renderItem}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooterList }
        />
      }
      <ConfirmDialog confirmText={t('DONGY')}
        cancelText={t('CANCEL')}
        onClosed={() => setIsShowConfirmDelete(false)}
        isVisible={isShowConfirmDelete}
        message={'Bạn có chắc chắn muốn xóa địa chỉ ?'}
        title={''}
        onConfirm={() => {
          setIsShowConfirmDelete(false)
          setTimeout(() => { onDelete(index).then(r => {}) }, 100)
        }}
      />
    </SafeAreaView>
  )
})
